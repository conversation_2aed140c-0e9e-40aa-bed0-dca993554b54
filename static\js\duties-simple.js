// كشف الواجبات - نسخة مبسطة
console.log('🚀 تحميل ملف duties-simple.js...');

// العناوين الافتراضية
const DEFAULT_HEADERS = ['الرقم', 'موقع الواجب', 'من 6 مساءً إلى 10 ليلاً', 'من 10 ليلاً إلى 2 ليلاً', 'من 2 ليلاً إلى 6 صباحاً', 'من 6 صباحاً إلى 10 صباحاً', 'من 10 صباحاً إلى 2 ظهراً', 'من 2 ظهراً إلى 6 مساءً', 'ملاحظات'];
const DEFAULT_PATROL_HEADERS = ['الرقم', 'موقع الواجب', '12 ليلاً إلى 6 صباحاً', '6 صباحاً إلى 12 ظهراً', '12 ظهراً إلى 6 مساءً', 'ملاحظات'];
const DEFAULT_SHIFTS_HEADERS = ['الرقم', 'موقع الواجب', '2 ظهراً إلى 10 ليلاً', '10 ليلاً إلى 6 صباحاً', '6 صباحاً إلى 2 ظهراً', 'ملاحظات المناوبين'];

// بيانات الجداول
let dutyData = {
    headers: [...DEFAULT_HEADERS],
    rows: []
};

let patrolData = {
    headers: [...DEFAULT_PATROL_HEADERS],
    rows: []
};

let shiftsData = {
    headers: [...DEFAULT_SHIFTS_HEADERS],
    rows: []
};

// قاعدة بيانات المواقع والأفراد
let locationsDatabase = [];
let locationPersonnelMap = {};

// تحميل المواقع
async function loadLocations() {
    try {
        const response = await fetch('/duties/api/get-locations');
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                locationsDatabase = data.locations;
                console.log(`✅ تم تحميل ${data.locations.length} موقع`);

                // تحديث جميع القوائم المنسدلة للمواقع
                updateAllLocationSelects();
            }
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل المواقع:', error);
    }
}

// دالة تحديث جميع القوائم المنسدلة للمواقع
function updateAllLocationSelects() {
    console.log('🔄 تحديث جميع القوائم المنسدلة للمواقع...');

    // تحديث قوائم الجدول الرئيسي
    const mainLocationSelects = document.querySelectorAll('#dutyTable .location-select');
    mainLocationSelects.forEach(select => {
        updateLocationSelect(select);
    });

    // تحديث قوائم جدول الدوريات
    const patrolLocationSelects = document.querySelectorAll('#patrolTable .location-select');
    patrolLocationSelects.forEach(select => {
        updateLocationSelect(select);
    });

    // تحديث قوائم جدول المناوبين
    const shiftsLocationSelects = document.querySelectorAll('#shiftsTable .location-select');
    shiftsLocationSelects.forEach(select => {
        updateLocationSelect(select);
    });

    console.log(`✅ تم تحديث ${mainLocationSelects.length + patrolLocationSelects.length + shiftsLocationSelects.length} قائمة منسدلة`);
}

// دالة تحديث قائمة منسدلة واحدة مع منع التكرار
function updateLocationSelect(selectElement, tableType = 'duty') {
    if (!selectElement || !locationsDatabase || locationsDatabase.length === 0) {
        return;
    }

    // حفظ القيمة المحددة حالياً
    const currentValue = selectElement.value;

    // الحصول على جميع المواقع المختارة عالمياً
    const globalSelectedLocations = getAllSelectedLocationsGlobally();

    // مسح الخيارات الحالية
    const placeholderText = tableType === 'patrol' ? 'اختر موقع الدورية' :
                           tableType === 'shifts' ? 'اختر موقع المناوبة' :
                           'اختر موقع الواجب';
    selectElement.innerHTML = `<option value="">${placeholderText}</option>`;

    // إضافة المواقع مع منع التكرار
    locationsDatabase.forEach(location => {
        const locationName = location.name;
        const isSelectedGlobally = globalSelectedLocations.includes(locationName);
        const isCurrentSelection = locationName === currentValue;

        // إظهار الموقع فقط إذا لم يكن مختار في مكان آخر أو كان مختار في القائمة الحالية
        if (!isSelectedGlobally || isCurrentSelection) {
            const option = document.createElement('option');
            option.value = location.name;
            option.textContent = location.name;
            if (location.name === currentValue) {
                option.selected = true;
            }
            selectElement.appendChild(option);
        }
    });

    // استعادة القيمة المحددة إذا كانت موجودة
    if (currentValue) {
        selectElement.value = currentValue;
    }
}

// تحميل أفراد الموقع
async function loadPersonnelForLocation(locationId, rowIndex) {
    console.log(`🔄 تحميل أفراد الموقع ${locationId} للصف ${rowIndex}`);

    // فحص شامل لمعرف الموقع
    if (!locationId || locationId === '' || locationId === 'undefined' || locationId === 'null') {
        console.log(`⚠️ معرف الموقع فارغ أو غير صحيح: "${locationId}" (نوع: ${typeof locationId})`);
        clearPersonnelSelectsInRow(rowIndex);
        return [];
    }

    // تنظيف معرف الموقع
    const cleanLocationId = String(locationId).trim();

    // فحص وجود نص عربي أو أحرف غير رقمية
    if (/[\u0600-\u06FF]/.test(cleanLocationId) || /[a-zA-Z]/.test(cleanLocationId)) {
        console.log(`⚠️ معرف الموقع يحتوي على نص: "${cleanLocationId}" - سيتم تجاهل تحميل الأفراد`);
        clearPersonnelSelectsInRow(rowIndex);
        return [];
    }

    // التحقق من أنه رقم صحيح
    if (isNaN(cleanLocationId)) {
        console.log(`⚠️ معرف الموقع ليس رقم: "${cleanLocationId}"`);
        clearPersonnelSelectsInRow(rowIndex);
        return [];
    }

    // تحويل إلى رقم للتأكد
    const numericLocationId = parseInt(cleanLocationId);
    if (isNaN(numericLocationId) || numericLocationId <= 0) {
        console.log(`⚠️ معرف الموقع ليس رقم صحيح: ${cleanLocationId} -> ${numericLocationId}`);
        clearPersonnelSelectsInRow(rowIndex);
        return [];
    }

    try {
        // التحقق من البيانات المحفوظة مسبقاً
        if (locationPersonnelMap[numericLocationId]) {
            console.log('✅ استخدام البيانات المحفوظة مسبقاً');
            updatePersonnelSelectsInRow(rowIndex, locationPersonnelMap[numericLocationId]);
            return;
        }

        console.log(`📡 محاولة جلب أفراد الموقع من الخادم للموقع رقم: ${numericLocationId}`);

        const response = await fetch(`/duties/api/get-location-personnel/${numericLocationId}`);
        console.log(`📡 استجابة الخادم - Status: ${response.status}`);

        if (response.ok) {
            const data = await response.json();
            console.log('📄 بيانات الاستجابة:', data);

            if (data.success) {
                if (data.personnel && data.personnel.length > 0) {
                    console.log(`✅ تم جلب ${data.personnel.length} فرد من قاعدة البيانات`);
                    locationPersonnelMap[numericLocationId] = data.personnel;
                    updatePersonnelSelectsInRow(rowIndex, data.personnel);
                } else {
                    console.log('⚠️ لا يوجد أفراد مرتبطين بهذا الموقع في قاعدة البيانات');
                    clearPersonnelSelectsInRow(rowIndex);
                }
            } else {
                console.log('❌ فشل في جلب الأفراد:', data.message || data.error);
                clearPersonnelSelectsInRow(rowIndex);
            }
        } else {
            console.log(`❌ خطأ في الاستجابة: ${response.status} - ${response.statusText}`);
            clearPersonnelSelectsInRow(rowIndex);
        }

    } catch (error) {
        console.error('❌ خطأ عام في تحميل أفراد الموقع:', error);
        clearPersonnelSelectsInRow(rowIndex);
    }
}

// تم حذف دالة الأفراد الافتراضيين - سيتم استخدام البيانات الحقيقية فقط

// دالة لمسح قوائم الأفراد في صف معين
function clearPersonnelSelectsInRow(rowIndex) {
    console.log(`🧹 مسح قوائم الأفراد في الصف ${rowIndex}`);

    const row = document.querySelector(`#dutyTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) {
        console.error(`❌ لم يتم العثور على الصف ${rowIndex + 1}`);
        return;
    }

    const personnelSelects = row.querySelectorAll('.personnel-select');
    personnelSelects.forEach(select => {
        select.innerHTML = '<option value="">اختر الفرد</option>';
        select.disabled = true;
    });
}

// دالة لتنسيق اسم الفرد مع الرتبة
function formatPersonName(person) {
    if (!person) return '';

    // إذا كان لديه display_name جاهز، استخدمه
    if (person.display_name) {
        return person.display_name;
    }

    // تنسيق الاسم: الرتبة + الاسم
    const rank = person.rank || '';
    const name = person.name || '';

    if (rank && name) {
        return `${rank} ${name}`;
    } else if (name) {
        return name;
    } else {
        return `فرد ${person.id}`;
    }
}

// دالة للحصول على جميع الأفراد المختارين في جميع الجداول
function getAllSelectedPersonnel() {
    const selectedIds = new Set();

    // الجدول الرئيسي
    const mainTable = document.getElementById('dutyTable');
    if (mainTable) {
        const selects = mainTable.querySelectorAll('.personnel-select');
        selects.forEach(select => {
            if (select.value && select.value !== '') {
                selectedIds.add(select.value);
            }
        });
    }

    // جدول الدوريات
    const patrolTable = document.getElementById('patrolTable');
    if (patrolTable) {
        const selects = patrolTable.querySelectorAll('.personnel-select');
        selects.forEach(select => {
            if (select.value && select.value !== '') {
                selectedIds.add(select.value);
            }
        });
    }

    // جدول المناوبين
    const shiftsTable = document.getElementById('shiftsTable');
    if (shiftsTable) {
        const selects = shiftsTable.querySelectorAll('.personnel-select');
        selects.forEach(select => {
            if (select.value && select.value !== '') {
                selectedIds.add(select.value);
            }
        });
    }

    return Array.from(selectedIds);
}

// تحديث قوائم الأفراد في صف - نسخة محسنة مع منع التكرار
function updatePersonnelSelectsInRow(rowIndex, personnel, tableType = 'duty') {
    console.log(`🔄 تحديث قوائم الأفراد للصف ${rowIndex} في جدول ${tableType}`);

    const tableId = tableType === 'patrol' ? 'patrolTable' :
                   tableType === 'shifts' ? 'shiftsTable' : 'dutyTable';

    const row = document.querySelector(`#${tableId} tbody tr:nth-child(${rowIndex + 1})`);
    console.log('📍 الصف الموجود:', row);

    if (!row) {
        console.error(`❌ لم يتم العثور على الصف ${rowIndex + 1} في جدول ${tableType}`);
        return;
    }

    const personnelSelects = row.querySelectorAll('.personnel-select');
    console.log(`👥 عدد قوائم الأفراد الموجودة: ${personnelSelects.length}`);

    // حفظ الاختيارات الحالية قبل التحديث
    const currentSelections = [];

    // الحصول على البيانات المناسبة حسب نوع الجدول
    const dataSource = tableType === 'patrol' ? patrolData :
                      tableType === 'shifts' ? shiftsData : dutyData;

    const rowData = dataSource.rows[rowIndex];
    if (rowData) {
        // خانات الأفراد من العمود 2 إلى نهاية الصف
        for (let colIndex = 2; colIndex < rowData.length; colIndex++) {
            currentSelections.push(rowData[colIndex] || '');
        }
        console.log(`💾 الاختيارات المحفوظة من البيانات: [${currentSelections.join(', ')}]`);
    } else {
        // إذا لم توجد بيانات محفوظة، استخدم القيم الحالية
        personnelSelects.forEach(select => {
            currentSelections.push(select.value);
        });
        console.log(`💾 الاختيارات الحالية من القوائم: [${currentSelections.join(', ')}]`);
    }

    // الحصول على جميع الأفراد المختارين في جميع الجداول
    const globalSelectedPersonnel = getAllSelectedPersonnel();
    console.log(`🌐 الأفراد المختارين في جميع الجداول: [${globalSelectedPersonnel.join(', ')}]`);

    personnelSelects.forEach((select, index) => {
        console.log(`🔄 تحديث القائمة ${index + 1}`);
        const currentValue = currentSelections[index] || select.value;
        select.disabled = false;
        select.innerHTML = '<option value="">اختر الفرد</option>';

        // الحصول على الأفراد المختارين في نفس الصف (باستثناء القائمة الحالية)
        const selectedPersonnelInRow = getSelectedPersonnelInRow(row, select);
        console.log(`🚫 الأفراد المختارين في الصف: [${selectedPersonnelInRow.join(', ')}]`);

        personnel.forEach(person => {
            const personId = person.id.toString();

            // شروط إظهار الفرد:
            // 1. غير مختار في نفس الصف (باستثناء القائمة الحالية)
            // 2. غير مختار في جداول أخرى (باستثناء إذا كان مختار في القائمة الحالية)
            const isSelectedInCurrentRow = selectedPersonnelInRow.includes(personId);
            const isSelectedGlobally = globalSelectedPersonnel.includes(personId);
            const isCurrentSelection = personId === currentValue;

            const shouldShow = (!isSelectedInCurrentRow && !isSelectedGlobally) || isCurrentSelection;

            if (shouldShow) {
                const option = document.createElement('option');
                option.value = person.id;
                // استخدام الدالة الجديدة لتنسيق الاسم
                option.textContent = formatPersonName(person);

                if (person.id == currentValue) {
                    option.selected = true;
                    console.log(`✅ تم استعادة اختيار الفرد: ${formatPersonName(person)} (ID: ${person.id}) في القائمة ${index + 1}`);
                }

                select.appendChild(option);
            } else {
                console.log(`🚫 تم إخفاء الفرد ${formatPersonName(person)} (مختار في مكان آخر)`);
            }
        });

        // إضافة مستمع للأحداث لتحديث القوائم عند تغيير الاختيار
        select.addEventListener('change', function() {
            console.log(`🔄 تغيير اختيار في القائمة ${index + 1}: ${this.value}`);

            // تحديث البيانات
            if (dataSource.rows[rowIndex]) {
                dataSource.rows[rowIndex][index + 2] = this.value;
            }

            // تحديث جميع القوائم في جميع الجداول لمنع التكرار
            setTimeout(() => {
                refreshAllPersonnelSelects();
            }, 100);

            // حفظ البيانات
            saveDataToLocalStorage();
        });
    });

    console.log(`✅ تم تحديث قوائم الأفراد للصف ${rowIndex} في جدول ${tableType}`);
}

// دوال مخصصة لكل جدول
function updatePatrolPersonnelSelects(row, personnel) {
    const rowIndex = Array.from(row.parentNode.children).indexOf(row);
    updatePersonnelSelectsInRow(rowIndex, personnel, 'patrol');
}

function updateShiftsPersonnelSelects(row, personnel) {
    const rowIndex = Array.from(row.parentNode.children).indexOf(row);
    updatePersonnelSelectsInRow(rowIndex, personnel, 'shifts');
}

// دالة لتحديث جميع قوائم الأفراد في جميع الجداول
function refreshAllPersonnelSelects() {
    console.log('🔄 تحديث جميع قوائم الأفراد في جميع الجداول...');

    // تحديث الجدول الرئيسي
    const mainTable = document.getElementById('dutyTable');
    if (mainTable) {
        const rows = mainTable.querySelectorAll('tbody tr');
        rows.forEach((row, rowIndex) => {
            const locationSelect = row.querySelector('.location-select');
            if (locationSelect && locationSelect.value) {
                const locationId = getLocationIdByName(locationSelect.value);
                if (locationId && locationPersonnelMap[locationId]) {
                    updatePersonnelSelectsInRow(rowIndex, locationPersonnelMap[locationId], 'duty');
                }
            }
        });
    }

    // تحديث جدول الدوريات
    const patrolTable = document.getElementById('patrolTable');
    if (patrolTable) {
        const rows = patrolTable.querySelectorAll('tbody tr');
        rows.forEach((row, rowIndex) => {
            const locationSelect = row.querySelector('.location-select');
            if (locationSelect && locationSelect.value) {
                const locationId = getLocationIdByName(locationSelect.value);
                if (locationId && locationPersonnelMap[locationId]) {
                    updatePersonnelSelectsInRow(rowIndex, locationPersonnelMap[locationId], 'patrol');
                }
            }
        });
    }

    // تحديث جدول المناوبين
    const shiftsTable = document.getElementById('shiftsTable');
    if (shiftsTable) {
        const rows = shiftsTable.querySelectorAll('tbody tr');
        rows.forEach((row, rowIndex) => {
            const locationSelect = row.querySelector('.location-select');
            if (locationSelect && locationSelect.value) {
                const locationId = getLocationIdByName(locationSelect.value);
                if (locationId && locationPersonnelMap[locationId]) {
                    updatePersonnelSelectsInRow(rowIndex, locationPersonnelMap[locationId], 'shifts');
                }
            }
        });
    }
}

// دالة مساعدة للحصول على معرف الموقع من الاسم
function getLocationIdByName(locationName) {
    const location = locationsDatabase?.find(loc => loc.name === locationName);
    return location ? location.id : null;
}

// دالة للحصول على الأفراد المختارين في نفس الصف
function getSelectedPersonnelInRow(row, excludeSelect = null) {
    const selectedIds = [];
    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach(select => {
        // تجاهل القائمة المحددة (القائمة التي يتم تحديثها حالياً)
        if (select !== excludeSelect && select.value && select.value !== '') {
            selectedIds.push(select.value);
        }
    });

    return selectedIds;
}

// دالة للحصول على جميع الأفراد المختارين في جميع الجداول
function getAllSelectedPersonnelGlobally() {
    const selectedPersonnel = [];

    // البحث في جميع الجداول
    const tables = ['dutyTable', 'patrolTable', 'shiftsTable'];

    tables.forEach(tableId => {
        const table = document.getElementById(tableId);
        if (table) {
            const personnelSelects = table.querySelectorAll('.personnel-select');
            personnelSelects.forEach(select => {
                if (select.value && select.value !== '') {
                    selectedPersonnel.push(select.value);
                }
            });
        }
    });

    return selectedPersonnel;
}

// دالة للحصول على جميع المواقع المختارة في جميع الجداول
function getAllSelectedLocationsGlobally() {
    const selectedLocations = [];

    // البحث في جميع الجداول
    const tables = ['dutyTable', 'patrolTable', 'shiftsTable'];

    tables.forEach(tableId => {
        const table = document.getElementById(tableId);
        if (table) {
            const locationSelects = table.querySelectorAll('.location-select');
            locationSelects.forEach(select => {
                if (select.value && select.value !== '') {
                    selectedLocations.push(select.value);
                }
            });
        }
    });

    return selectedLocations;
}

// دالة لتحديث جميع قوائم المواقع في جميع الجداول
function refreshAllLocationSelects() {
    console.log('🔄 تحديث جميع قوائم المواقع في جميع الجداول...');

    // تحديث الجدول الرئيسي
    const mainTable = document.getElementById('dutyTable');
    if (mainTable) {
        const locationSelects = mainTable.querySelectorAll('.location-select');
        locationSelects.forEach(select => {
            updateLocationSelect(select, 'duty');
        });
    }

    // تحديث جدول الدوريات
    const patrolTable = document.getElementById('patrolTable');
    if (patrolTable) {
        const locationSelects = patrolTable.querySelectorAll('.location-select');
        locationSelects.forEach(select => {
            updateLocationSelect(select, 'patrol');
        });
    }

    // تحديث جدول المناوبين
    const shiftsTable = document.getElementById('shiftsTable');
    if (shiftsTable) {
        const locationSelects = shiftsTable.querySelectorAll('.location-select');
        locationSelects.forEach(select => {
            updateLocationSelect(select, 'shifts');
        });
    }
}

// دالة لتحديث قوائم الأفراد في صف بعد تغيير الاختيار
function refreshPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#dutyTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    // الحصول على الموقع المختار في هذا الصف
    const locationSelect = row.querySelector('select:first-child');
    if (!locationSelect || !locationSelect.value) return;

    const locationId = locationSelect.value;

    // الحصول على أفراد الموقع من الذاكرة المؤقتة
    const personnel = locationPersonnelMap[locationId];
    if (!personnel) return;

    console.log(`🔄 تحديث قوائم الأفراد في الصف ${rowIndex} بعد تغيير الاختيار`);
    updatePersonnelSelectsInRow(rowIndex, personnel);
}

// تفريغ قوائم الأفراد في صف
function clearPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#dutyTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');
    
    personnelSelects.forEach(select => {
        select.disabled = true;
        select.innerHTML = '<option value="">اختر موقع أولاً</option>';
        select.value = '';
    });
}

// إنشاء رأس الجدول
function generateTableHeader() {
    const thead = document.getElementById('tableHeader');
    if (!thead) return;
    
    thead.innerHTML = '';
    const headerRow = document.createElement('tr');
    
    dutyData.headers.forEach((header, index) => {
        const th = document.createElement('th');
        th.className = 'text-center';

        if (index === 0) {
            // عمود الرقم - بدون أيقونات
            th.textContent = header;
        } else {
            // باقي الأعمدة مع أيقونات التحكم
            th.innerHTML = `
                <input type="text" class="editable-header" value="${header}"
                       onchange="updateHeader(${index}, this.value)"
                       onblur="updateHeader(${index}, this.value)">
                <div class="column-controls">
                    <button type="button" class="control-btn btn-add" onclick="addColumnAfter(${index})" title="إضافة عمود">
                        <i class="fas fa-plus"></i>
                    </button>
                    ${dutyData.headers.length > 3 ? `
                    <button type="button" class="control-btn btn-delete" onclick="deleteColumn(${index})" title="حذف العمود">
                        <i class="fas fa-times"></i>
                    </button>
                    ` : ''}
                </div>
            `;
        }

        headerRow.appendChild(th);
    });
    
    thead.appendChild(headerRow);
}

// إنشاء محتوى الجدول
function generateTableBody() {
    const tbody = document.getElementById('receiptTableBody');
    if (!tbody) return;
    
    // إنشاء صفوف افتراضية إذا لم تكن موجودة
    if (dutyData.rows.length === 0) {
        for (let i = 0; i < 10; i++) {
            dutyData.rows.push(Array(dutyData.headers.length).fill(''));
        }
    }
    
    tbody.innerHTML = '';
    
    dutyData.rows.forEach((rowData, rowIndex) => {
        const row = document.createElement('tr');
        
        rowData.forEach((cellData, cellIndex) => {
            const td = document.createElement('td');
            td.className = 'text-center align-middle';
            
            if (cellIndex === 0) {
                // عمود الرقم مع أيقونات التحكم
                td.className = 'text-center align-middle row-number-cell';
                td.innerHTML = `
                    <span class="row-number">${rowIndex + 1}</span>
                    <div class="row-controls">
                        <button type="button" class="control-btn btn-add" onclick="addRowAfter(${rowIndex})" title="إضافة صف">
                            <i class="fas fa-plus"></i>
                        </button>
                        ${dutyData.rows.length > 1 ? `
                        <button type="button" class="control-btn btn-delete" onclick="deleteRow(${rowIndex})" title="حذف الصف">
                            <i class="fas fa-times"></i>
                        </button>
                        ` : ''}
                    </div>
                `;
            } else if (cellIndex === 1) {
                // عمود الموقع
                td.innerHTML = `
                    <select class="form-select location-select" onchange="handleLocationChange(${rowIndex}, ${cellIndex}, this.value);">
                        <option value="">اختر الموقع</option>
                    </select>
                `;
                
                const select = td.querySelector('.location-select');
                if (locationsDatabase && locationsDatabase.length > 0) {
                    locationsDatabase.forEach(location => {
                        const option = document.createElement('option');
                        option.value = location.name; // استخدام اسم الموقع بدلاً من الرقم
                        option.textContent = location.name;
                        if (location.name === cellData) { // مقارنة بالاسم
                            option.selected = true;
                        }
                        select.appendChild(option);
                    });
                } else {
                    console.warn('⚠️ قاعدة بيانات المواقع غير متاحة');
                }
            } else if (cellIndex >= 2 && cellIndex <= 7) {
                // خانات الأفراد
                td.innerHTML = `
                    <select class="form-select personnel-select"
                            onchange="handlePersonnelChange(${rowIndex}, ${cellIndex}, this.value);"
                            disabled>
                        <option value="">اختر موقع أولاً</option>
                    </select>
                `;
            } else {
                // خانة الملاحظات
                td.innerHTML = `
                    <input type="text" class="form-control" value="${cellData}" onchange="updateCell(${rowIndex}, ${cellIndex}, this.value)">
                `;
            }
            
            row.appendChild(td);
        });
        
        tbody.appendChild(row);
    });
}

// دالة خاصة لمعالجة تغيير الأفراد في الجدول الرئيسي
function handlePersonnelChange(rowIndex, cellIndex, value) {
    console.log(`👤 تغيير فرد في [${rowIndex}, ${cellIndex}]: "${value}"`);

    // التأكد من وجود الصف في البيانات
    if (!dutyData.rows[rowIndex]) {
        console.log(`⚠️ الصف ${rowIndex} غير موجود، إنشاء صف جديد`);
        dutyData.rows[rowIndex] = Array(dutyData.headers.length).fill('');
    }

    // تحديث البيانات فوراً
    const oldValue = dutyData.rows[rowIndex][cellIndex];
    dutyData.rows[rowIndex][cellIndex] = value;

    console.log(`📝 تحديث بيانات الفرد: "${oldValue}" → "${value}"`);

    // حفظ فوري ومتعدد للتأكد من عدم فقدان البيانات
    console.log('💾 حفظ فوري ومتعدد بعد تغيير الفرد');

    // حفظ فوري في localStorage
    saveDataToLocalStorage();

    // حفظ فوري في قاعدة البيانات (بدون تأخير)
    saveToDatabaseAndLocal();

    // حفظ إضافي بعد ثانية للتأكد
    setTimeout(() => {
        saveDataToLocalStorage();
        saveToDatabaseAndLocal();
    }, 1000);

    // حفظ إضافي بعد 3 ثوان للتأكد المطلق
    setTimeout(() => {
        saveDataToLocalStorage();
        saveToDatabaseAndLocal();
    }, 3000);

    // تحديث القوائم الأخرى في نفس الصف
    setTimeout(() => {
        refreshPersonnelSelectsInRow(rowIndex);
    }, 300);

    console.log(`✅ تم حفظ اختيار الفرد ${value} في الخانة [${rowIndex}, ${cellIndex}]`);
}

// دالة خاصة لمعالجة تغيير الأفراد في جدول الدوريات
function handlePatrolPersonnelChange(rowIndex, cellIndex, value) {
    console.log(`👤 تغيير فرد في جدول الدوريات [${rowIndex}, ${cellIndex}]: "${value}"`);

    // التأكد من وجود الصف في البيانات
    if (!patrolData.rows[rowIndex]) {
        console.log(`⚠️ الصف ${rowIndex} غير موجود في جدول الدوريات، إنشاء صف جديد`);
        patrolData.rows[rowIndex] = Array(patrolData.headers.length).fill('');
    }

    // تحديث البيانات فوراً
    const oldValue = patrolData.rows[rowIndex][cellIndex];
    patrolData.rows[rowIndex][cellIndex] = value;

    console.log(`📝 تحديث بيانات فرد الدورية: "${oldValue}" → "${value}"`);
    console.log(`📋 بيانات الدورية بعد التحديث:`, patrolData.rows[rowIndex]);

    // حفظ فوري في localStorage
    saveDataToLocalStorage();

    // حفظ فوري في قاعدة البيانات
    console.log(`💾 حفظ فوري لاختيار فرد الدورية ${value} في [${rowIndex}, ${cellIndex}]`);

    // حفظ البيانات الكاملة
    setTimeout(() => {
        savePatrolDataToServer();
        saveDutyDataToServer(); // حفظ في الجدول الرئيسي أيضاً
    }, 200);

    // تحديث القوائم الأخرى في نفس الصف
    setTimeout(() => {
        refreshPatrolPersonnelSelectsInRow(rowIndex);
    }, 300);

    console.log(`✅ تم حفظ اختيار فرد الدورية ${value} في الخانة [${rowIndex}, ${cellIndex}]`);
}

// دالة خاصة لمعالجة تغيير الأفراد في جدول المناوبين
function handleShiftsPersonnelChange(rowIndex, cellIndex, value) {
    console.log(`👤 تغيير فرد في جدول المناوبين [${rowIndex}, ${cellIndex}]: "${value}"`);

    // التأكد من وجود الصف في البيانات
    if (!shiftsData.rows[rowIndex]) {
        console.log(`⚠️ الصف ${rowIndex} غير موجود في جدول المناوبين، إنشاء صف جديد`);
        shiftsData.rows[rowIndex] = Array(shiftsData.headers.length).fill('');
    }

    // تحديث البيانات فوراً
    const oldValue = shiftsData.rows[rowIndex][cellIndex];
    shiftsData.rows[rowIndex][cellIndex] = value;

    console.log(`📝 تحديث بيانات فرد المناوبة: "${oldValue}" → "${value}"`);
    console.log(`📋 بيانات المناوبة بعد التحديث:`, shiftsData.rows[rowIndex]);

    // حفظ فوري في localStorage
    saveDataToLocalStorage();

    // حفظ فوري في قاعدة البيانات
    console.log(`💾 حفظ فوري لاختيار فرد المناوبة ${value} في [${rowIndex}, ${cellIndex}]`);

    // حفظ البيانات الكاملة
    setTimeout(() => {
        saveShiftsDataToServer();
        saveDutyDataToServer(); // حفظ في الجدول الرئيسي أيضاً
    }, 200);

    // تحديث القوائم الأخرى في نفس الصف
    setTimeout(() => {
        refreshShiftsPersonnelSelectsInRow(rowIndex);
    }, 300);

    console.log(`✅ تم حفظ اختيار فرد المناوبة ${value} في الخانة [${rowIndex}, ${cellIndex}]`);
}

// دالة لتحديث قوائم الأفراد في صف معين - جدول الدوريات
function refreshPatrolPersonnelSelectsInRow(rowIndex) {
    const locationId = patrolData.rows[rowIndex] ? patrolData.rows[rowIndex][1] : null;
    if (!locationId) return;

    // الحصول على أفراد الموقع من الذاكرة المؤقتة
    const personnel = locationPersonnelMap[locationId];
    if (!personnel) return;

    console.log(`🔄 تحديث قوائم أفراد الدوريات في الصف ${rowIndex} بعد تغيير الاختيار`);
    updatePatrolPersonnelSelectsInRow(rowIndex, personnel);
}

// دالة لتحديث قوائم الأفراد في صف معين - جدول المناوبين
function refreshShiftsPersonnelSelectsInRow(rowIndex) {
    const locationId = shiftsData.rows[rowIndex] ? shiftsData.rows[rowIndex][1] : null;
    if (!locationId) return;

    // الحصول على أفراد الموقع من الذاكرة المؤقتة
    const personnel = locationPersonnelMap[locationId];
    if (!personnel) return;

    console.log(`🔄 تحديث قوائم أفراد المناوبين في الصف ${rowIndex} بعد تغيير الاختيار`);
    updateShiftsPersonnelSelectsInRow(rowIndex, personnel);
}

// تحديث قوائم الأفراد في صف - جدول الدوريات (استخدام النظام الجديد)
function updatePatrolPersonnelSelectsInRow(rowIndex, personnel) {
    console.log(`🔄 تحديث قوائم أفراد الدوريات للصف ${rowIndex} - استخدام النظام الجديد`);
    updatePersonnelSelectsInRow(rowIndex, personnel, 'patrol');
}

// تحديث قوائم الأفراد في صف - جدول المناوبين (استخدام النظام الجديد)
function updateShiftsPersonnelSelectsInRow(rowIndex, personnel) {
    console.log(`🔄 تحديث قوائم أفراد المناوبين للصف ${rowIndex} - استخدام النظام الجديد`);
    updatePersonnelSelectsInRow(rowIndex, personnel, 'shifts');
}

// دالة للحصول على الأفراد المختارين في نفس الصف - جدول الدوريات
function getSelectedPatrolPersonnelInRow(row, excludeSelect = null) {
    const selectedIds = [];
    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach(select => {
        if (select !== excludeSelect && select.value) {
            selectedIds.push(select.value);
        }
    });

    return selectedIds;
}

// دالة للحصول على الأفراد المختارين في نفس الصف - جدول المناوبين
function getSelectedShiftsPersonnelInRow(row, excludeSelect = null) {
    const selectedIds = [];
    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach(select => {
        if (select !== excludeSelect && select.value) {
            selectedIds.push(select.value);
        }
    });

    return selectedIds;
}

// تحديث خلية
function updateCell(rowIndex, cellIndex, value) {
    if (dutyData.rows[rowIndex]) {
        const oldValue = dutyData.rows[rowIndex][cellIndex];
        dutyData.rows[rowIndex][cellIndex] = value;

        console.log(`📝 تحديث خلية [${rowIndex}, ${cellIndex}]: "${oldValue}" → "${value}"`);

        // إذا كان التغيير في خانة فرد، قم بتحديث القوائم لتجنب التكرار
        if (cellIndex >= 2 && cellIndex <= 7) {
            console.log(`👥 تحديث قوائم الأفراد للصف ${rowIndex} بعد اختيار الفرد: ${value}`);

            // تأكيد حفظ اختيار الفرد
            if (value) {
                console.log(`✅ تم اختيار الفرد ${value} في الخانة [${rowIndex}, ${cellIndex}]`);
            }

            refreshPersonnelSelectsInRow(rowIndex);
        }

        // حفظ البيانات تلقائياً في localStorage
        saveDataToLocalStorage();

        // حفظ في قاعدة البيانات بعد تأخير قصير لتجنب الطلبات المتكررة
        clearTimeout(window.cellUpdateTimeout);
        window.cellUpdateTimeout = setTimeout(() => {
            saveToDatabaseAndLocal();
        }, 1500);

        console.log(`💾 تم جدولة حفظ تحديث الخلية [${rowIndex}, ${cellIndex}]`);
    }
}

// معالجة تغيير الموقع في الجدول الرئيسي
function handleLocationChange(rowIndex, cellIndex, locationName) {
    console.log(`🔄 تم اختيار موقع: ${locationName} للصف: ${rowIndex}`);

    // تحديث البيانات فوراً باسم الموقع
    updateCell(rowIndex, cellIndex, locationName);

    // البحث عن رقم الموقع من اسمه لتحميل الأفراد
    let locationId = null;
    if (locationName && locationsDatabase) {
        const location = locationsDatabase.find(loc => loc.name === locationName);
        if (location) {
            locationId = location.id;
            console.log(`📍 تم العثور على الموقع: "${locationName}" -> ID: ${locationId}`);
        } else {
            console.log(`⚠️ لم يتم العثور على الموقع: "${locationName}"`);
        }
    }

    // تحميل أفراد الموقع باستخدام الرقم
    if (locationId) {
        loadPersonnelForLocation(locationId, rowIndex);
    } else {
        clearPersonnelSelectsInRow(rowIndex);
    }

    // حفظ فوري ومتعدد للتأكد من عدم فقدان البيانات
    console.log('💾 حفظ فوري ومتعدد بعد تغيير الموقع');

    // حفظ فوري في localStorage
    saveDataToLocalStorage();

    // حفظ فوري في قاعدة البيانات (بدون تأخير)
    saveToDatabaseAndLocal();

    // حفظ إضافي بعد ثانية للتأكد
    setTimeout(() => {
        saveDataToLocalStorage();
        saveToDatabaseAndLocal();
    }, 1000);

    // حفظ إضافي بعد 3 ثوان للتأكد المطلق
    setTimeout(() => {
        saveDataToLocalStorage();
        saveToDatabaseAndLocal();
    }, 3000);

    // تحديث جميع قوائم المواقع لمنع التكرار
    setTimeout(() => {
        refreshAllLocationSelects();
    }, 100);

    // إظهار إشعار حفظ الموقع
    showSaveNotification(`تم حفظ الموقع للصف ${rowIndex + 1}`, 'success');

    console.log(`✅ تم حفظ الموقع ${locationName} للصف ${rowIndex + 1} بنجاح`);
}

// معالجة تغيير الموقع في جدول الدوريات
function handlePatrolLocationChange(rowIndex, cellIndex, locationName) {
    console.log(`🔄 تم اختيار موقع دورية: ${locationName} للصف: ${rowIndex}`);

    // تحديث البيانات باسم الموقع
    updatePatrolCell(rowIndex, cellIndex, locationName);

    // البحث عن رقم الموقع من اسمه لتحميل الأفراد
    let locationId = null;
    if (locationName && locationsDatabase) {
        const location = locationsDatabase.find(loc => loc.name === locationName);
        if (location) {
            locationId = location.id;
            console.log(`📍 تم العثور على موقع الدورية: "${locationName}" -> ID: ${locationId}`);
        } else {
            console.log(`⚠️ لم يتم العثور على موقع الدورية: "${locationName}"`);
        }
    }

    // تحميل أفراد الموقع باستخدام الرقم
    if (locationId) {
        loadPersonnelForPatrolLocation(locationId, rowIndex);
    } else {
        clearPatrolPersonnelSelectsInRow(rowIndex);
    }

    // تحديث جميع قوائم المواقع لمنع التكرار
    setTimeout(() => {
        refreshAllLocationSelects();
    }, 100);

    // حفظ فوري
    console.log('💾 حفظ فوري بعد تغيير موقع الدورية');
    saveDataToLocalStorage();
    savePatrolDataToServer();
    saveDutyDataToServer();
}

// معالجة تغيير الموقع في جدول المناوبات
function handleShiftsLocationChange(rowIndex, cellIndex, locationName) {
    console.log(`🔄 تم اختيار موقع مناوبة: ${locationName} للصف: ${rowIndex}`);

    // تحديث البيانات باسم الموقع
    updateShiftsCell(rowIndex, cellIndex, locationName);

    // البحث عن رقم الموقع من اسمه لتحميل الأفراد
    let locationId = null;
    if (locationName && locationsDatabase) {
        const location = locationsDatabase.find(loc => loc.name === locationName);
        if (location) {
            locationId = location.id;
            console.log(`📍 تم العثور على موقع المناوبة: "${locationName}" -> ID: ${locationId}`);
        } else {
            console.log(`⚠️ لم يتم العثور على موقع المناوبة: "${locationName}"`);
        }
    }

    // تحميل أفراد الموقع باستخدام الرقم
    if (locationId) {
        loadPersonnelForShiftsLocation(locationId, rowIndex);
    } else {
        clearShiftsPersonnelSelectsInRow(rowIndex);
    }

    // تحديث جميع قوائم المواقع لمنع التكرار
    setTimeout(() => {
        refreshAllLocationSelects();
    }, 100);

    // حفظ فوري
    console.log('💾 حفظ فوري بعد تغيير موقع المناوبة');
    saveDataToLocalStorage();
    saveShiftsDataToServer();
    saveDutyDataToServer();
}

// دوال لمسح قوائم الأفراد
function clearPatrolPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');
    personnelSelects.forEach(select => {
        select.disabled = true;
        select.innerHTML = '<option value="">اختر موقع أولاً</option>';
        select.value = '';
    });
}

function clearShiftsPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');
    personnelSelects.forEach(select => {
        select.disabled = true;
        select.innerHTML = '<option value="">اختر موقع أولاً</option>';
        select.value = '';
    });
}

// دالة فحص وإصلاح حالة البيانات
function validateAndFixDataStructure() {
    console.log('🔧 فحص وإصلاح هيكل البيانات...');

    // فحص البيانات الرئيسية
    if (!dutyData || !Array.isArray(dutyData.rows)) {
        console.warn('⚠️ إصلاح البيانات الرئيسية');
        if (!dutyData) dutyData = { headers: [], rows: [] };
        if (!Array.isArray(dutyData.rows)) dutyData.rows = [];
    }

    // إضافة صف واحد فارغ إذا لم توجد صفوف
    if (dutyData.rows.length === 0) {
        dutyData.rows = [Array(dutyData.headers.length || 8).fill('')];
        console.log('✅ تم إضافة صف فارغ للجدول الرئيسي');
    }

    // فحص بيانات الدوريات
    if (!patrolData || !Array.isArray(patrolData.rows)) {
        console.warn('⚠️ إصلاح بيانات الدوريات');
        if (!patrolData) patrolData = { headers: [], rows: [] };
        if (!Array.isArray(patrolData.rows)) patrolData.rows = [];
    }

    // إضافة صف واحد فارغ إذا لم توجد صفوف
    if (patrolData.rows.length === 0) {
        patrolData.rows = [Array(patrolData.headers.length || 6).fill('')];
        console.log('✅ تم إضافة صف فارغ لجدول الدوريات');
    }

    // فحص بيانات المناوبين
    if (!shiftsData || !Array.isArray(shiftsData.rows)) {
        console.warn('⚠️ إصلاح بيانات المناوبين');
        if (!shiftsData) shiftsData = { headers: [], rows: [] };
        if (!Array.isArray(shiftsData.rows)) shiftsData.rows = [];
    }

    // إضافة صف واحد فارغ إذا لم توجد صفوف
    if (shiftsData.rows.length === 0) {
        shiftsData.rows = [Array(shiftsData.headers.length || 6).fill('')];
        console.log('✅ تم إضافة صف فارغ لجدول المناوبين');
    }

    console.log('✅ تم فحص وإصلاح هيكل البيانات:', {
        'الجدول الرئيسي': dutyData.rows.length + ' صف',
        'جدول الدوريات': patrolData.rows.length + ' صف',
        'جدول المناوبين': shiftsData.rows.length + ' صف'
    });
}

// دالة لإظهار معلومات مفصلة عن حالة البيانات
function showDataStatus() {
    const status = {
        'الجدول الرئيسي': {
            'عدد الصفوف': dutyData?.rows?.length || 0,
            'صحيح': Array.isArray(dutyData?.rows),
            'فارغ': !dutyData?.rows?.length
        },
        'جدول الدوريات': {
            'عدد الصفوف': patrolData?.rows?.length || 0,
            'صحيح': Array.isArray(patrolData?.rows),
            'فارغ': !patrolData?.rows?.length
        },
        'جدول المناوبين': {
            'عدد الصفوف': shiftsData?.rows?.length || 0,
            'صحيح': Array.isArray(shiftsData?.rows),
            'فارغ': !shiftsData?.rows?.length
        }
    };

    console.table(status);
    return status;
}

// دالة تحميل جميع البيانات من الخادم
async function loadAllDataFromServer() {
    try {
        console.log('🔄 تحميل جميع البيانات من الخادم...');

        // تحميل البيانات بالتوازي
        const promises = [];

        // تحميل البيانات الرئيسية
        promises.push(loadDutyDataFromServer());
        promises.push(loadPatrolDataFromServer());
        promises.push(loadShiftsDataFromServer());

        // انتظار جميع العمليات
        await Promise.all(promises);

        console.log('✅ تم تحميل جميع البيانات من الخادم');
        return true;

    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات من الخادم:', error);
        return false;
    }
}

// دالة لإضافة أزرار التشخيص
function addDiagnosticButtons() {
    // البحث عن منطقة الأزرار
    const controlsArea = document.querySelector('.controls-area') || document.querySelector('.table-controls');

    if (controlsArea) {
        // إضافة زر فحص البيانات
        const diagnosticBtn = document.createElement('button');
        diagnosticBtn.className = 'btn btn-info btn-sm me-2';
        diagnosticBtn.innerHTML = '<i class="fas fa-stethoscope"></i> فحص البيانات';
        diagnosticBtn.onclick = () => {
            const status = showDataStatus();
            alert(`حالة البيانات:\n\nالجدول الرئيسي: ${status['الجدول الرئيسي']['عدد الصفوف']} صف\nجدول الدوريات: ${status['جدول الدوريات']['عدد الصفوف']} صف\nجدول المناوبين: ${status['جدول المناوبين']['عدد الصفوف']} صف`);
        };

        // إضافة زر إعادة تحميل
        const reloadBtn = document.createElement('button');
        reloadBtn.className = 'btn btn-warning btn-sm me-2';
        reloadBtn.innerHTML = '<i class="fas fa-sync"></i> إعادة تحميل';
        reloadBtn.onclick = () => {
            if (confirm('هل تريد إعادة تحميل جميع البيانات من الخادم؟')) {
                loadAllDataFromServer().then(() => {
                    generateTable();
                    generatePatrolTable();
                    generateShiftsTable();
                    alert('تم إعادة تحميل البيانات بنجاح');
                });
            }
        };

        controlsArea.appendChild(diagnosticBtn);
        controlsArea.appendChild(reloadBtn);
    }
}

// دالة حفظ فوري ودائم للحذف
async function saveDeletedDataPermanently() {
    try {
        console.log('💾 حفظ فوري ودائم للبيانات المحذوفة...');

        // حفظ في جميع الأنظمة بشكل متزامن
        const promises = [];

        // 1. حفظ في النظام الأساسي
        promises.push(saveDutyDataToServer());
        promises.push(savePatrolDataToServer());
        promises.push(saveShiftsDataToServer());

        // 2. حفظ في النظام المحسن إذا كان متاحاً
        if (typeof improvedAutoSave === 'function') {
            promises.push(improvedAutoSave());
        }

        // 3. حفظ في النظام المحسن البديل
        if (typeof saveEnhancedData === 'function') {
            promises.push(saveEnhancedData());
        }

        // انتظار جميع عمليات الحفظ
        await Promise.all(promises);

        // 4. حفظ في localStorage
        saveDataToLocalStorage();

        console.log('✅ تم الحفظ الفوري والدائم في جميع الأنظمة');

        // إضافة علامة زمنية للحذف لمنع التحميل القديم
        localStorage.setItem('lastDeleteTime', Date.now().toString());

        return true;

    } catch (error) {
        console.error('❌ خطأ في الحفظ الفوري:', error);
        return false;
    }
}

// حفظ البيانات في localStorage وقاعدة البيانات (محسن)
function saveDataToLocalStorage() {
    try {
        const dataToSave = {
            dutyData: dutyData,
            patrolData: patrolData,
            shiftsData: shiftsData,
            formData: {
                dayName: document.getElementById('dayName').value,
                hijriDate: document.getElementById('hijriDate').value,
                gregorianDate: document.getElementById('gregorianDate').value,
                receiptNumber: document.getElementById('receiptNumber').value
            },
            timestamp: new Date().toISOString()
        };

        // استخدام النظام المحسن إذا كان متاحاً
        if (typeof improvedAutoSave === 'function') {
            console.log('🔧 استخدام النظام المحسن للحفظ...');
            // تحديث البيانات العامة أولاً
            dutyData = dataToSave.dutyData;
            patrolData = dataToSave.patrolData;
            shiftsData = dataToSave.shiftsData;
            // استخدام الحفظ المحسن
            improvedAutoSave();
            return;
        }

        // حفظ محلي فوري (الطريقة التقليدية)
        localStorage.setItem('dutyFormData', JSON.stringify(dataToSave));
        console.log('💾 تم حفظ البيانات محلياً');

        // حفظ في قاعدة البيانات (مع تأخير لتجنب الطلبات المتكررة)
        saveDraftToDatabase(dataToSave);

    } catch (error) {
        console.error('❌ خطأ في حفظ البيانات محلياً:', error);
    }
}

// متغير لتأخير الحفظ
let saveTimeout = null;

// حفظ المسودة في قاعدة البيانات
function saveDraftToDatabase(data) {
    // إلغاء الطلب السابق إذا كان موجوداً
    if (saveTimeout) {
        clearTimeout(saveTimeout);
    }

    // تأخير الحفظ لمدة ثانية واحدة فقط لتجنب الطلبات المتكررة
    saveTimeout = setTimeout(async () => {
        try {
            console.log('🌐 حفظ المسودة في قاعدة البيانات...');

            const response = await fetch('/duties/api/save-draft', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    console.log('✅ تم حفظ المسودة في قاعدة البيانات');
                    // إشعار صغير وسريع
                    showNotification('💾 تم الحفظ', 'success', 2000);
                } else {
                    console.error('❌ فشل في حفظ المسودة:', result.error);
                    showNotification('❌ فشل في الحفظ: ' + result.error, 'error');
                }
            } else {
                console.error('❌ خطأ في الاستجابة:', response.status);
                showNotification('❌ خطأ في الحفظ', 'error');
            }
        } catch (error) {
            console.error('❌ خطأ في حفظ المسودة:', error);
            showNotification('❌ خطأ في الاتصال', 'error');
        }
    }, 1000); // تأخير لمدة ثانية واحدة
}

// استرجاع البيانات من localStorage وقاعدة البيانات (محسن)
async function loadDataFromLocalStorage() {
    try {
        console.log('📥 بدء تحميل البيانات (النظام المحسن)...');

        // فحص ما إذا كانت البيانات قد تم مسحها مؤخراً
        const dataCleared = localStorage.getItem('dutyDataCleared');
        if (dataCleared) {
            const clearTime = parseInt(dataCleared);
            const now = Date.now();
            // إذا تم المسح خلال آخر 30 ثانية، لا تحمل البيانات
            if (now - clearTime < 30000) {
                console.log('⚠️ تم مسح البيانات مؤخراً - تجاهل التحميل');
                return false;
            } else {
                // إزالة العلامة بعد انتهاء المدة
                localStorage.removeItem('dutyDataCleared');
            }
        }

        // فحص ما إذا كان هناك حذف حديث
        const lastDeleteTime = localStorage.getItem('lastDeleteTime');
        if (lastDeleteTime) {
            const deleteTime = parseInt(lastDeleteTime);
            const now = Date.now();
            // إذا تم الحذف خلال آخر 60 ثانية، لا تحمل البيانات القديمة
            if (now - deleteTime < 60000) {
                console.log('⚠️ تم حذف بيانات مؤخراً - تجاهل التحميل لمنع استعادة البيانات المحذوفة');
                return false;
            }
        }

        // استخدام النظام المحسن إذا كان متاحاً
        if (typeof loadFromDatabaseFirst === 'function') {
            console.log('🔧 استخدام النظام المحسن للتحميل...');
            return await loadFromDatabaseFirst();
        }

        // أولاً: محاولة تحميل من قاعدة البيانات (الطريقة التقليدية)
        const databaseData = await loadDraftFromDatabase();
        if (databaseData) {
            console.log('✅ تم استرجاع البيانات من قاعدة البيانات');
            return true;
        }

        // ثانياً: التحميل من localStorage كبديل
        const savedData = localStorage.getItem('dutyFormData');
        if (savedData) {
            const data = JSON.parse(savedData);
            applyLoadedData(data);
            console.log('✅ تم استرجاع البيانات المحفوظة محلياً');
            return true;
        }
    } catch (error) {
        console.error('❌ خطأ في استرجاع البيانات:', error);
    }
    return false;
}

// تحميل المسودة من قاعدة البيانات
async function loadDraftFromDatabase() {
    try {
        console.log('🌐 تحميل المسودة من قاعدة البيانات...');

        const response = await fetch('/duties/api/load-draft');
        if (response.ok) {
            const result = await response.json();
            if (result.success && result.data) {
                applyLoadedData(result.data);
                showNotification(`تم تحميل المسودة (آخر تحديث: ${result.last_updated})`, 'info');
                return true;
            } else {
                console.log('⚠️ لا توجد مسودة محفوظة في قاعدة البيانات');
                return false;
            }
        } else {
            console.error('❌ خطأ في تحميل المسودة:', response.status);
            return false;
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل المسودة من قاعدة البيانات:', error);
        return false;
    }
}

// تطبيق البيانات المحملة
function applyLoadedData(data) {
    console.log('🔄 تطبيق البيانات المحملة:', data);

    // استرجاع بيانات الجداول
    if (data.dutyData) {
        dutyData = data.dutyData;
        console.log('✅ تم استرجاع بيانات الواجبات:', dutyData.rows.length, 'صف');
        console.log('📋 بيانات الواجبات:', dutyData.rows);
    }
    if (data.patrolData) {
        patrolData = data.patrolData;
        console.log('✅ تم استرجاع بيانات الدوريات:', patrolData.rows.length, 'صف');
        console.log('📋 بيانات الدوريات:', patrolData.rows);
    }
    if (data.shiftsData) {
        shiftsData = data.shiftsData;
        console.log('✅ تم استرجاع بيانات المناوبات:', shiftsData.rows.length, 'صف');
        console.log('📋 بيانات المناوبات:', shiftsData.rows);
    }

    // استرجاع بيانات النموذج
    if (data.formData) {
        if (data.formData.dayName) document.getElementById('dayName').value = data.formData.dayName;
        if (data.formData.hijriDate) document.getElementById('hijriDate').value = data.formData.hijriDate;
        if (data.formData.gregorianDate) document.getElementById('gregorianDate').value = data.formData.gregorianDate;
        if (data.formData.receiptNumber) document.getElementById('receiptNumber').value = data.formData.receiptNumber;
        console.log('✅ تم استرجاع بيانات النموذج');
    }

    // إعادة إنشاء الجداول لتطبيق البيانات المحملة
    setTimeout(() => {
        console.log('🔄 إعادة إنشاء الجداول مع البيانات المحملة...');
        generateTable();
        generatePatrolTable();
        generateShiftsTable();

        // تحميل أفراد المواقع المختارة
        restoreLocationPersonnel();
    }, 100);
}

// دالة لاستعادة أفراد المواقع المختارة
async function restoreLocationPersonnel() {
    console.log('🔄 استعادة أفراد المواقع المختارة...');

    // انتظار قصير للتأكد من أن الجداول تم إنشاؤها
    await new Promise(resolve => setTimeout(resolve, 200));

    // استعادة أفراد الواجبات الرئيسية
    for (let rowIndex = 0; rowIndex < dutyData.rows.length; rowIndex++) {
        const row = dutyData.rows[rowIndex];
        const locationId = row[1]; // عمود الموقع

        if (locationId) {
            console.log(`🔄 استعادة الصف ${rowIndex}: موقع ${locationId}`);

            // تحميل أفراد الموقع أولاً
            await loadPersonnelForLocation(locationId, rowIndex);

            // انتظار قصير ثم تطبيق الاختيارات المحفوظة
            await new Promise(resolve => setTimeout(resolve, 100));

            // تطبيق اختيارات الأفراد المحفوظة
            await restorePersonnelSelections(rowIndex, row, 'duty');
        }
    }

    // استعادة أفراد الدوريات
    for (let rowIndex = 0; rowIndex < patrolData.rows.length; rowIndex++) {
        const row = patrolData.rows[rowIndex];
        const locationId = row[1]; // عمود الموقع

        if (locationId) {
            await loadPersonnelForPatrolLocation(locationId, rowIndex);
            await new Promise(resolve => setTimeout(resolve, 100));
            await restorePersonnelSelections(rowIndex, row, 'patrol');
        }
    }

    // استعادة أفراد المناوبات
    for (let rowIndex = 0; rowIndex < shiftsData.rows.length; rowIndex++) {
        const row = shiftsData.rows[rowIndex];
        const locationId = row[1]; // عمود الموقع

        if (locationId) {
            await loadPersonnelForShiftsLocation(locationId, rowIndex);
            await new Promise(resolve => setTimeout(resolve, 100));
            await restorePersonnelSelections(rowIndex, row, 'shifts');
        }
    }

    console.log('✅ تم استعادة جميع أفراد المواقع والاختيارات');
}

// دالة لاستعادة اختيارات الأفراد المحفوظة
async function restorePersonnelSelections(rowIndex, rowData, tableType) {
    let tableSelector, startCol, endCol;

    // تحديد نوع الجدول والأعمدة
    switch (tableType) {
        case 'duty':
            tableSelector = '#dutyTable';
            startCol = 2;
            endCol = 7;
            break;
        case 'patrol':
            tableSelector = '#patrolTable';
            startCol = 2;
            endCol = 5;
            break;
        case 'shifts':
            tableSelector = '#shiftsTable';
            startCol = 2;
            endCol = 4;
            break;
        default:
            return;
    }

    const row = document.querySelector(`${tableSelector} tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) {
        console.error(`❌ لم يتم العثور على الصف ${rowIndex + 1} في ${tableSelector}`);
        return;
    }

    console.log(`🔄 استعادة اختيارات الأفراد للصف ${rowIndex} في ${tableType}`);

    // تطبيق اختيارات الأفراد
    for (let colIndex = startCol; colIndex <= endCol; colIndex++) {
        const personnelId = rowData[colIndex];
        if (personnelId) {
            const select = row.querySelector(`td:nth-child(${colIndex + 1}) select.personnel-select`);
            if (select) {
                // البحث عن الخيار المطابق وتحديده
                const option = select.querySelector(`option[value="${personnelId}"]`);
                if (option) {
                    select.value = personnelId;
                    console.log(`✅ تم استعادة اختيار الفرد ${personnelId} في العمود ${colIndex}`);
                } else {
                    console.warn(`⚠️ لم يتم العثور على الفرد ${personnelId} في القائمة`);
                }
            }
        }
    }
}

// دوال مساعدة لتحميل أفراد المواقع للجداول الأخرى
async function loadPersonnelForPatrolLocation(locationId, rowIndex) {
    try {
        const personnel = locationPersonnelMap[locationId];
        if (personnel) {
            const row = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1})`);
            if (row) {
                updatePatrolPersonnelSelects(row, personnel);
            }
        } else {
            await loadPersonnelForLocation(locationId, rowIndex);
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل أفراد الدوريات:', error);
    }
}

async function loadPersonnelForShiftsLocation(locationId, rowIndex) {
    try {
        const personnel = locationPersonnelMap[locationId];
        if (personnel) {
            const row = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1})`);
            if (row) {
                updateShiftsPersonnelSelects(row, personnel);
            }
        } else {
            await loadPersonnelForLocation(locationId, rowIndex);
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل أفراد المناوبات:', error);
    }
}

// دالة لإظهار الإشعارات
function showNotification(message, type = 'info', duration = 5000) {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 250px;
        max-width: 400px;
        font-size: 14px;
    `;

    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);

    // إزالة الإشعار تلقائياً
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, duration);
}

// إنشاء الجدول الرئيسي
function generateTable() {
    // إضافة class receipts-table للجدول الرئيسي لتوحيد التنسيق
    const dutyTable = document.getElementById('dutyTable');
    if (dutyTable) {
        dutyTable.classList.add('receipts-table');
        console.log('✅ تم إضافة class receipts-table للجدول الرئيسي');
    }

    generateTableHeader();
    generateTableBody();
}

// إنشاء جدول الدوريات
function generatePatrolTable() {
    // إضافة class receipts-table لجدول الدوريات لتطبيق نفس تنسيقات كشف الاستلامات
    const patrolTable = document.getElementById('patrolTable');
    if (patrolTable) {
        patrolTable.classList.add('receipts-table');
        console.log('✅ تم إضافة class receipts-table لجدول الدوريات');
        console.log('📋 Classes الحالية للجدول:', patrolTable.className);

        // التأكد من إضافة class بقوة
        if (!patrolTable.classList.contains('receipts-table')) {
            patrolTable.className += ' receipts-table';
            console.log('🔧 تم إضافة class بقوة');
        }
    } else {
        console.error('❌ لم يتم العثور على جدول الدوريات');
    }

    generatePatrolTableHeader();
    generatePatrolTableBody();

    // التحقق مرة أخرى بعد إنشاء الجدول
    setTimeout(() => {
        const table = document.getElementById('patrolTable');
        if (table) {
            console.log('🔍 فحص نهائي - Classes:', table.className);
            console.log('🔍 هل يحتوي على receipts-table؟', table.classList.contains('receipts-table'));
        }
    }, 100);
}

// إنشاء جدول المناوبين
function generateShiftsTable() {
    // إضافة class receipts-table لجدول المناوبين لتوحيد التنسيق
    const shiftsTable = document.getElementById('shiftsTable');
    if (shiftsTable) {
        shiftsTable.classList.add('receipts-table');
        console.log('✅ تم إضافة class receipts-table لجدول المناوبين');
    }

    generateShiftsTableHeader();
    generateShiftsTableBody();
}

// إنشاء رأس جدول الدوريات
function generatePatrolTableHeader() {
    const thead = document.getElementById('patrolTableHeader');
    if (!thead) return;

    thead.innerHTML = '';
    const headerRow = document.createElement('tr');

    patrolData.headers.forEach((header, index) => {
        const th = document.createElement('th');
        th.className = 'text-center';

        if (index === 0) {
            th.textContent = header;
        } else {
            th.innerHTML = `
                <input type="text" class="editable-header" value="${header}"
                       onchange="updatePatrolHeader(${index}, this.value)"
                       onblur="updatePatrolHeader(${index}, this.value)">
                <div class="column-controls">
                    <button type="button" class="control-btn btn-add" onclick="addPatrolColumnAfter(${index})" title="إضافة عمود">
                        <i class="fas fa-plus"></i>
                    </button>
                    ${patrolData.headers.length > 3 ? `
                    <button type="button" class="control-btn btn-delete" onclick="deletePatrolColumn(${index})" title="حذف العمود">
                        <i class="fas fa-times"></i>
                    </button>
                    ` : ''}
                </div>
            `;
        }

        headerRow.appendChild(th);
    });

    thead.appendChild(headerRow);
}

// إنشاء محتوى جدول الدوريات
function generatePatrolTableBody() {
    const tbody = document.getElementById('patrolTableBody');
    if (!tbody) return;

    // إنشاء صفوف افتراضية إذا لم تكن موجودة
    if (patrolData.rows.length === 0) {
        for (let i = 0; i < 6; i++) {
            patrolData.rows.push(Array(patrolData.headers.length).fill(''));
        }
    }

    tbody.innerHTML = '';

    patrolData.rows.forEach((rowData, rowIndex) => {
        const row = document.createElement('tr');

        rowData.forEach((cellData, cellIndex) => {
            const td = document.createElement('td');
            td.className = 'text-center align-middle';

            if (cellIndex === 0) {
                // عمود الرقم مع أيقونات التحكم
                td.className = 'text-center align-middle row-number-cell';
                td.innerHTML = `
                    <span class="row-number">${rowIndex + 1}</span>
                    <div class="row-controls">
                        <button type="button" class="control-btn btn-add" onclick="addPatrolRowAfter(${rowIndex})" title="إضافة صف">
                            <i class="fas fa-plus"></i>
                        </button>
                        ${patrolData.rows.length > 1 ? `
                        <button type="button" class="control-btn btn-delete" onclick="deletePatrolRow(${rowIndex})" title="حذف الصف">
                            <i class="fas fa-times"></i>
                        </button>
                        ` : ''}
                    </div>
                `;
            } else if (cellIndex === 1) {
                // عمود الموقع
                td.innerHTML = `
                    <select class="form-select location-select" onchange="handlePatrolLocationChange(${rowIndex}, ${cellIndex}, this.value);">
                        <option value="">اختر الموقع</option>
                    </select>
                `;

                const select = td.querySelector('.location-select');
                if (locationsDatabase && locationsDatabase.length > 0) {
                    locationsDatabase.forEach(location => {
                        const option = document.createElement('option');
                        option.value = location.name; // استخدام اسم الموقع بدلاً من الرقم
                        option.textContent = location.name;
                        if (location.name === cellData) { // مقارنة بالاسم
                            option.selected = true;
                        }
                        select.appendChild(option);
                    });
                } else {
                    console.warn('⚠️ قاعدة بيانات المواقع غير متاحة في جدول الدوريات');
                }
            } else if (cellIndex >= 2 && cellIndex < patrolData.headers.length - 1) {
                // خانات الأفراد (كل الأعمدة ما عدا الأخير الذي هو الملاحظات)
                td.innerHTML = `
                    <select class="form-select personnel-select" onchange="handlePatrolPersonnelChange(${rowIndex}, ${cellIndex}, this.value);" disabled>
                        <option value="">اختر موقع أولاً</option>
                    </select>
                `;
            } else {
                // خانة الملاحظات (العمود الأخير)
                td.innerHTML = `
                    <input type="text" class="form-control" value="${cellData}" onchange="updatePatrolCell(${rowIndex}, ${cellIndex}, this.value)">
                `;
            }

            row.appendChild(td);
        });

        tbody.appendChild(row);
    });
}

// إنشاء رأس جدول المناوبين
function generateShiftsTableHeader() {
    const thead = document.getElementById('shiftsTableHeader');
    if (!thead) return;

    thead.innerHTML = '';
    const headerRow = document.createElement('tr');

    shiftsData.headers.forEach((header, index) => {
        const th = document.createElement('th');
        th.className = 'text-center';

        if (index === 0) {
            th.textContent = header;
        } else {
            th.innerHTML = `
                <input type="text" class="editable-header" value="${header}"
                       onchange="updateShiftsHeader(${index}, this.value)"
                       onblur="updateShiftsHeader(${index}, this.value)">
                <div class="column-controls">
                    <button type="button" class="control-btn btn-add" onclick="addShiftsColumnAfter(${index})" title="إضافة عمود">
                        <i class="fas fa-plus"></i>
                    </button>
                    ${shiftsData.headers.length > 3 ? `
                    <button type="button" class="control-btn btn-delete" onclick="deleteShiftsColumn(${index})" title="حذف العمود">
                        <i class="fas fa-times"></i>
                    </button>
                    ` : ''}
                </div>
            `;
        }

        headerRow.appendChild(th);
    });

    thead.appendChild(headerRow);
}

// إنشاء محتوى جدول المناوبين
function generateShiftsTableBody() {
    const tbody = document.getElementById('shiftsTableBody');
    if (!tbody) return;

    // إنشاء صفوف افتراضية إذا لم تكن موجودة
    if (shiftsData.rows.length === 0) {
        for (let i = 0; i < 2; i++) {
            shiftsData.rows.push(Array(shiftsData.headers.length).fill(''));
        }
    }

    tbody.innerHTML = '';

    shiftsData.rows.forEach((rowData, rowIndex) => {
        const row = document.createElement('tr');

        rowData.forEach((cellData, cellIndex) => {
            const td = document.createElement('td');
            td.className = 'text-center align-middle';

            if (cellIndex === 0) {
                // عمود الرقم مع أيقونات التحكم
                td.className = 'text-center align-middle row-number-cell';
                td.innerHTML = `
                    <span class="row-number">${rowIndex + 1}</span>
                    <div class="row-controls">
                        <button type="button" class="control-btn btn-add" onclick="addShiftsRowAfter(${rowIndex})" title="إضافة صف">
                            <i class="fas fa-plus"></i>
                        </button>
                        ${shiftsData.rows.length > 1 ? `
                        <button type="button" class="control-btn btn-delete" onclick="deleteShiftsRow(${rowIndex})" title="حذف الصف">
                            <i class="fas fa-times"></i>
                        </button>
                        ` : ''}
                    </div>
                `;
            } else if (cellIndex === 1) {
                // عمود الموقع
                td.innerHTML = `
                    <select class="form-select location-select" onchange="handleShiftsLocationChange(${rowIndex}, ${cellIndex}, this.value);">
                        <option value="">اختر الموقع</option>
                    </select>
                `;

                const select = td.querySelector('.location-select');
                if (locationsDatabase && locationsDatabase.length > 0) {
                    locationsDatabase.forEach(location => {
                        const option = document.createElement('option');
                        option.value = location.name; // استخدام اسم الموقع بدلاً من الرقم
                        option.textContent = location.name;
                        if (location.name === cellData) { // مقارنة بالاسم
                            option.selected = true;
                        }
                        select.appendChild(option);
                    });
                } else {
                    console.warn('⚠️ قاعدة بيانات المواقع غير متاحة في جدول المناوبين');
                }
            } else if (cellIndex >= 2 && cellIndex <= 4) {
                // خانات الأفراد
                td.innerHTML = `
                    <select class="form-select personnel-select" onchange="handleShiftsPersonnelChange(${rowIndex}, ${cellIndex}, this.value);" disabled>
                        <option value="">اختر موقع أولاً</option>
                    </select>
                `;
            } else {
                // خانة الملاحظات
                td.innerHTML = `
                    <input type="text" class="form-control" value="${cellData}" onchange="updateShiftsCell(${rowIndex}, ${cellIndex}, this.value)">
                `;
            }

            row.appendChild(td);
        });

        tbody.appendChild(row);
    });
}

// تحديث خلايا الجداول الأخرى
function updatePatrolCell(rowIndex, cellIndex, value) {
    if (patrolData.rows[rowIndex]) {
        patrolData.rows[rowIndex][cellIndex] = value;

        // إذا كان التغيير في خانة فرد، قم بتحديث القوائم لتجنب التكرار
        if (cellIndex >= 2 && cellIndex <= 5) {
            refreshPatrolPersonnelSelectsInRow(rowIndex);
        }

        saveDataToLocalStorage();

        // حفظ في قاعدة البيانات
        savePatrolDataToServer();
    }
}

// تحميل أفراد الموقع - جدول الدوريات
async function loadPersonnelForPatrolLocation(locationId, rowIndex) {
    console.log(`🔄 تحميل أفراد الموقع ${locationId} للدوريات - الصف ${rowIndex}`);

    // فحص شامل لمعرف الموقع
    if (!locationId || locationId === '' || locationId === 'undefined' || locationId === 'null') {
        console.log(`⚠️ معرف الموقع فارغ أو غير صحيح: "${locationId}"`);
        clearPatrolPersonnelSelectsInRow(rowIndex);
        return;
    }

    // تنظيف معرف الموقع
    const cleanLocationId = String(locationId).trim();

    // فحص وجود نص عربي أو أحرف غير رقمية
    if (/[\u0600-\u06FF]/.test(cleanLocationId) || /[a-zA-Z]/.test(cleanLocationId)) {
        console.log(`⚠️ معرف الموقع يحتوي على نص: "${cleanLocationId}" - سيتم تجاهل تحميل الأفراد`);
        clearPatrolPersonnelSelectsInRow(rowIndex);
        return;
    }

    // التحقق من أنه رقم صحيح
    if (isNaN(cleanLocationId)) {
        console.log(`⚠️ معرف الموقع ليس رقم: "${cleanLocationId}"`);
        clearPatrolPersonnelSelectsInRow(rowIndex);
        return;
    }

    const numericLocationId = parseInt(cleanLocationId);
    if (isNaN(numericLocationId) || numericLocationId <= 0) {
        console.log(`⚠️ معرف الموقع ليس رقم صحيح: ${cleanLocationId} -> ${numericLocationId}`);
        clearPatrolPersonnelSelectsInRow(rowIndex);
        return;
    }

    try {
        if (locationPersonnelMap[numericLocationId]) {
            console.log('✅ استخدام البيانات المحفوظة مسبقاً');
            updatePatrolPersonnelSelectsInRow(rowIndex, locationPersonnelMap[numericLocationId]);
            return;
        }

        console.log(`📡 جلب أفراد الموقع من الخادم: /duties/api/get-location-personnel/${numericLocationId}`);
        const response = await fetch(`/duties/api/get-location-personnel/${numericLocationId}`);

        if (response.ok) {
            const data = await response.json();
            console.log('📄 استجابة الخادم:', data);

            if (data.success) {
                console.log(`✅ تم جلب ${data.personnel.length} فرد من الخادم`);
                locationPersonnelMap[locationId] = data.personnel;
                updatePatrolPersonnelSelectsInRow(rowIndex, data.personnel);
            } else {
                console.log('❌ فشل في جلب الأفراد:', data.error);
                clearPatrolPersonnelSelectsInRow(rowIndex);
            }
        } else {
            console.log('❌ خطأ في الاستجابة:', response.status);
            clearPatrolPersonnelSelectsInRow(rowIndex);
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل أفراد الموقع:', error);
        clearPatrolPersonnelSelectsInRow(rowIndex);
    }
}

// تحديث قوائم الأفراد في صف - جدول الدوريات
function updatePatrolPersonnelSelectsInRow(rowIndex, personnel) {
    console.log(`🔄 تحديث قوائم الأفراد للدوريات - الصف ${rowIndex}`);

    const row = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1})`);
    console.log('📍 الصف الموجود:', row);

    if (!row) {
        console.error(`❌ لم يتم العثور على الصف ${rowIndex + 1}`);
        return;
    }

    const personnelSelects = row.querySelectorAll('.personnel-select');
    console.log(`👥 عدد قوائم الأفراد الموجودة: ${personnelSelects.length}`);

    personnelSelects.forEach((select, index) => {
        console.log(`🔄 تحديث القائمة ${index + 1}`);
        const currentValue = select.value;
        select.disabled = false;
        select.innerHTML = '<option value="">اختر الفرد</option>';

        // الحصول على جميع الأفراد المختارين عالمياً
        const globalSelectedPersonnel = getAllSelectedPersonnelGlobally();

        personnel.forEach(person => {
            const personId = person.id.toString();
            const isSelectedGlobally = globalSelectedPersonnel.includes(personId);
            const isCurrentSelection = personId === currentValue;

            // إظهار الفرد فقط إذا لم يكن مختاراً في مكان آخر أو كان مختاراً في القائمة الحالية
            if (!isSelectedGlobally || isCurrentSelection) {
                const option = document.createElement('option');
                option.value = person.id;
                option.textContent = `${person.name} (${person.rank})`;
                if (person.id == currentValue) {
                    option.selected = true;
                }
                select.appendChild(option);
            }
        });

        console.log(`✅ تم تحديث القائمة ${index + 1} بـ ${personnel.length} فرد`);
    });
}

// تفريغ قوائم الأفراد في صف - جدول الدوريات
function clearPatrolPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach(select => {
        select.disabled = true;
        select.innerHTML = '<option value="">اختر موقع أولاً</option>';
        select.value = '';
    });
}

function updateShiftsCell(rowIndex, cellIndex, value) {
    if (shiftsData.rows[rowIndex]) {
        shiftsData.rows[rowIndex][cellIndex] = value;

        // إذا كان التغيير في خانة فرد، قم بتحديث القوائم لتجنب التكرار
        if (cellIndex >= 2 && cellIndex <= 4) {
            refreshShiftsPersonnelSelectsInRow(rowIndex);
        }

        saveDataToLocalStorage();

        // حفظ في قاعدة البيانات
        saveShiftsDataToServer();
    }
}

// دوال مساعدة لتحديث قوائم الأفراد في الجداول الأخرى
function refreshPatrolPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const locationSelect = row.querySelector('select:first-child');
    if (!locationSelect || !locationSelect.value) return;

    const locationId = locationSelect.value;
    const personnel = locationPersonnelMap[locationId];
    if (!personnel) return;

    console.log(`🔄 تحديث قوائم أفراد الدوريات في الصف ${rowIndex}`);
    updatePatrolPersonnelSelects(row, personnel);
}

function refreshShiftsPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const locationSelect = row.querySelector('select:first-child');
    if (!locationSelect || !locationSelect.value) return;

    const locationId = locationSelect.value;
    const personnel = locationPersonnelMap[locationId];
    if (!personnel) return;

    console.log(`🔄 تحديث قوائم أفراد المناوبات في الصف ${rowIndex}`);
    updateShiftsPersonnelSelects(row, personnel);
}

// دوال لتحديث قوائم الأفراد مع تجنب التكرار
function updatePatrolPersonnelSelects(row, personnel) {
    const personnelSelects = row.querySelectorAll('.personnel-select');

    // حفظ الاختيارات الحالية
    const currentSelections = [];
    personnelSelects.forEach(select => {
        currentSelections.push(select.value);
    });

    personnelSelects.forEach((select, index) => {
        const currentValue = currentSelections[index] || select.value;
        const selectedPersonnelInRow = getSelectedPersonnelInRow(row, select);

        select.innerHTML = '<option value="">اختر الفرد</option>';

        // الحصول على جميع الأفراد المختارين عالمياً
        const globalSelectedPersonnel = getAllSelectedPersonnelGlobally();

        personnel.forEach(person => {
            const personId = person.id.toString();

            // السماح بإظهار الفرد إذا:
            // 1. لم يتم اختياره في أي مكان آخر
            // 2. أو كان مختاراً في القائمة الحالية
            const isSelectedGlobally = globalSelectedPersonnel.includes(personId);
            const isCurrentSelection = personId === currentValue;
            const isSelectedInCurrentRow = selectedPersonnelInRow.includes(personId);

            if (!isSelectedGlobally || isCurrentSelection) {
                const option = document.createElement('option');
                option.value = person.id;
                option.textContent = person.display_name || `${person.name} (${person.rank})`;
                if (person.id == currentValue) {
                    option.selected = true;
                }
                select.appendChild(option);
            }
        });

        // التأكد من تطبيق الاختيار
        if (currentValue && select.value !== currentValue) {
            select.value = currentValue;
        }
    });
}

function updateShiftsPersonnelSelects(row, personnel) {
    const personnelSelects = row.querySelectorAll('.personnel-select');

    // حفظ الاختيارات الحالية
    const currentSelections = [];
    personnelSelects.forEach(select => {
        currentSelections.push(select.value);
    });

    personnelSelects.forEach((select, index) => {
        const currentValue = currentSelections[index] || select.value;
        const selectedPersonnelInRow = getSelectedPersonnelInRow(row, select);

        select.innerHTML = '<option value="">اختر الفرد</option>';

        // الحصول على جميع الأفراد المختارين عالمياً
        const globalSelectedPersonnel = getAllSelectedPersonnelGlobally();

        personnel.forEach(person => {
            const personId = person.id.toString();

            // السماح بإظهار الفرد إذا:
            // 1. لم يتم اختياره في أي مكان آخر
            // 2. أو كان مختاراً في القائمة الحالية
            const isSelectedGlobally = globalSelectedPersonnel.includes(personId);
            const isCurrentSelection = personId === currentValue;

            if (!isSelectedGlobally || isCurrentSelection) {
                const option = document.createElement('option');
                option.value = person.id;
                option.textContent = person.display_name || `${person.name} (${person.rank})`;
                if (person.id == currentValue) {
                    option.selected = true;
                }
                select.appendChild(option);
            }
        });

        // التأكد من تطبيق الاختيار
        if (currentValue && select.value !== currentValue) {
            select.value = currentValue;
        }
    });
}

// تحميل أفراد الموقع - جدول المناوبين
async function loadPersonnelForShiftsLocation(locationId, rowIndex) {
    console.log(`🔄 تحميل أفراد الموقع ${locationId} للمناوبين - الصف ${rowIndex}`);

    // فحص شامل لمعرف الموقع
    if (!locationId || locationId === '' || locationId === 'undefined' || locationId === 'null') {
        console.log(`⚠️ معرف الموقع فارغ أو غير صحيح: "${locationId}"`);
        clearShiftsPersonnelSelectsInRow(rowIndex);
        return;
    }

    // تنظيف معرف الموقع
    const cleanLocationId = String(locationId).trim();

    // فحص وجود نص عربي أو أحرف غير رقمية
    if (/[\u0600-\u06FF]/.test(cleanLocationId) || /[a-zA-Z]/.test(cleanLocationId)) {
        console.log(`⚠️ معرف الموقع يحتوي على نص: "${cleanLocationId}" - سيتم تجاهل تحميل الأفراد`);
        clearShiftsPersonnelSelectsInRow(rowIndex);
        return;
    }

    // التحقق من أنه رقم صحيح
    if (isNaN(cleanLocationId)) {
        console.log(`⚠️ معرف الموقع ليس رقم: "${cleanLocationId}"`);
        clearShiftsPersonnelSelectsInRow(rowIndex);
        return;
    }

    const numericLocationId = parseInt(cleanLocationId);
    if (isNaN(numericLocationId) || numericLocationId <= 0) {
        console.log(`⚠️ معرف الموقع ليس رقم صحيح: ${cleanLocationId} -> ${numericLocationId}`);
        clearShiftsPersonnelSelectsInRow(rowIndex);
        return;
    }

    try {
        if (locationPersonnelMap[numericLocationId]) {
            console.log('✅ استخدام البيانات المحفوظة مسبقاً');
            updateShiftsPersonnelSelectsInRow(rowIndex, locationPersonnelMap[numericLocationId]);
            return;
        }

        console.log(`📡 جلب أفراد الموقع من الخادم: /duties/api/get-location-personnel/${numericLocationId}`);
        const response = await fetch(`/duties/api/get-location-personnel/${numericLocationId}`);

        if (response.ok) {
            const data = await response.json();
            console.log('📄 استجابة الخادم:', data);

            if (data.success) {
                console.log(`✅ تم جلب ${data.personnel.length} فرد من الخادم`);
                locationPersonnelMap[locationId] = data.personnel;
                updateShiftsPersonnelSelectsInRow(rowIndex, data.personnel);
            } else {
                console.log('❌ فشل في جلب الأفراد:', data.error);
                clearShiftsPersonnelSelectsInRow(rowIndex);
            }
        } else {
            console.log('❌ خطأ في الاستجابة:', response.status);
            clearShiftsPersonnelSelectsInRow(rowIndex);
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل أفراد الموقع:', error);
        clearShiftsPersonnelSelectsInRow(rowIndex);
    }
}

// تحديث قوائم الأفراد في صف - جدول المناوبين
function updateShiftsPersonnelSelectsInRow(rowIndex, personnel) {
    console.log(`🔄 تحديث قوائم الأفراد للمناوبين - الصف ${rowIndex}`);

    const row = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1})`);
    console.log('📍 الصف الموجود:', row);

    if (!row) {
        console.error(`❌ لم يتم العثور على الصف ${rowIndex + 1}`);
        return;
    }

    const personnelSelects = row.querySelectorAll('.personnel-select');
    console.log(`👥 عدد قوائم الأفراد الموجودة: ${personnelSelects.length}`);

    personnelSelects.forEach((select, index) => {
        console.log(`🔄 تحديث القائمة ${index + 1}`);
        const currentValue = select.value;
        select.disabled = false;
        select.innerHTML = '<option value="">اختر الفرد</option>';

        // الحصول على جميع الأفراد المختارين عالمياً
        const globalSelectedPersonnel = getAllSelectedPersonnelGlobally();

        personnel.forEach(person => {
            const personId = person.id.toString();
            const isSelectedGlobally = globalSelectedPersonnel.includes(personId);
            const isCurrentSelection = personId === currentValue;

            // إظهار الفرد فقط إذا لم يكن مختاراً في مكان آخر أو كان مختاراً في القائمة الحالية
            if (!isSelectedGlobally || isCurrentSelection) {
                const option = document.createElement('option');
                option.value = person.id;
                option.textContent = `${person.name} (${person.rank})`;
                if (person.id == currentValue) {
                    option.selected = true;
                }
                select.appendChild(option);
            }
        });

        console.log(`✅ تم تحديث القائمة ${index + 1} بـ ${personnel.length} فرد`);
    });
}

// تفريغ قوائم الأفراد في صف - جدول المناوبين
function clearShiftsPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach(select => {
        select.disabled = true;
        select.innerHTML = '<option value="">اختر موقع أولاً</option>';
        select.value = '';
    });
}

// تحميل أفراد الموقع للجداول الأخرى
async function loadPersonnelForPatrolLocation(locationId, rowIndex) {
    if (!locationId) {
        clearPatrolPersonnelSelectsInRow(rowIndex);
        return;
    }

    try {
        if (locationPersonnelMap[locationId]) {
            updatePatrolPersonnelSelectsInRow(rowIndex, locationPersonnelMap[locationId]);
            return;
        }

        const response = await fetch(`/duties/api/get-location-personnel/${locationId}`);
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                locationPersonnelMap[locationId] = data.personnel;
                updatePatrolPersonnelSelectsInRow(rowIndex, data.personnel);
            } else {
                clearPatrolPersonnelSelectsInRow(rowIndex);
            }
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل أفراد الموقع:', error);
        clearPatrolPersonnelSelectsInRow(rowIndex);
    }
}

async function loadPersonnelForShiftsLocation(locationId, rowIndex) {
    if (!locationId) {
        clearShiftsPersonnelSelectsInRow(rowIndex);
        return;
    }

    try {
        if (locationPersonnelMap[locationId]) {
            updateShiftsPersonnelSelectsInRow(rowIndex, locationPersonnelMap[locationId]);
            return;
        }

        const response = await fetch(`/duties/api/get-location-personnel/${locationId}`);
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                locationPersonnelMap[locationId] = data.personnel;
                updateShiftsPersonnelSelectsInRow(rowIndex, data.personnel);
            } else {
                clearShiftsPersonnelSelectsInRow(rowIndex);
            }
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل أفراد الموقع:', error);
        clearShiftsPersonnelSelectsInRow(rowIndex);
    }
}

// تم حذف الدالة المكررة - استخدام الدالة الموحدة updatePersonnelSelectsInRow

function updateShiftsPersonnelSelectsInRow(rowIndex, personnel) {
    const row = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach(select => {
        const currentValue = select.value;
        select.disabled = false;
        select.innerHTML = '<option value="">اختر الفرد</option>';

        // الحصول على جميع الأفراد المختارين عالمياً
        const globalSelectedPersonnel = getAllSelectedPersonnelGlobally();

        personnel.forEach(person => {
            const personId = person.id.toString();
            const isSelectedGlobally = globalSelectedPersonnel.includes(personId);
            const isCurrentSelection = personId === currentValue;

            // إظهار الفرد فقط إذا لم يكن مختاراً في مكان آخر أو كان مختاراً في القائمة الحالية
            if (!isSelectedGlobally || isCurrentSelection) {
                const option = document.createElement('option');
                option.value = person.id;
                option.textContent = `${person.name} (${person.rank})`;
                if (person.id == currentValue) {
                    option.selected = true;
                }
                select.appendChild(option);
            }
        });
    });
}

// تفريغ قوائم الأفراد للجداول الأخرى
function clearPatrolPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach(select => {
        select.disabled = true;
        select.innerHTML = '<option value="">اختر موقع أولاً</option>';
        select.value = '';
    });
}

function clearShiftsPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach(select => {
        select.disabled = true;
        select.innerHTML = '<option value="">اختر موقع أولاً</option>';
        select.value = '';
    });
}

// دوال إضافة للجداول المختلفة (للتوافق مع HTML)
function addPatrolRow() {
    const newRow = Array(patrolData.headers.length).fill('');
    patrolData.rows.push(newRow);
    generatePatrolTable();
    console.log('✅ تم إضافة صف جديد لجدول الدوريات');
}

function addPatrolColumn() {
    patrolData.headers.push('عمود جديد');
    patrolData.rows.forEach(row => row.push(''));
    generatePatrolTable();
    console.log('✅ تم إضافة عمود جديد لجدول الدوريات');
}

function addShiftsRow() {
    const newRow = Array(shiftsData.headers.length).fill('');
    shiftsData.rows.push(newRow);
    generateShiftsTable();
    console.log('✅ تم إضافة صف جديد لجدول المناوبين');
}

function addShiftsColumn() {
    shiftsData.headers.push('عمود جديد');
    shiftsData.rows.forEach(row => row.push(''));
    generateShiftsTable();
    console.log('✅ تم إضافة عمود جديد لجدول المناوبين');
}

function resetHeaders() {
    if (confirm('هل أنت متأكد من تفريغ الكشف؟')) {
        dutyData.rows = [Array(dutyData.headers.length).fill('')];
        generateTable();
        console.log('✅ تم تفريغ الكشف الرئيسي');
    }
}

function resetPatrolTable() {
    if (confirm('هل أنت متأكد من تفريغ جدول الدوريات؟')) {
        patrolData.rows = [Array(patrolData.headers.length).fill('')];
        generatePatrolTable();
        console.log('✅ تم تفريغ جدول الدوريات');
    }
}

function resetShiftsTable() {
    if (confirm('هل أنت متأكد من تفريغ جدول المناوبين؟')) {
        shiftsData.rows = [Array(shiftsData.headers.length).fill('')];
        generateShiftsTable();
        console.log('✅ تم تفريغ جدول المناوبين');
    }
}

// دوال الحفظ والتصدير
function saveReceipt() {
    try {
        saveDutyDataToServer();
        savePatrolDataToServer();
        saveShiftsDataToServer();
        alert('تم حفظ جميع البيانات بنجاح');
        console.log('✅ تم حفظ جميع البيانات');
    } catch (error) {
        console.error('❌ خطأ في الحفظ:', error);
        alert('حدث خطأ في الحفظ');
    }
}

function clearAllData() {
    if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        // مسح البيانات
        dutyData.rows = [Array(dutyData.headers.length).fill('')];
        patrolData.rows = [Array(patrolData.headers.length).fill('')];
        shiftsData.rows = [Array(shiftsData.headers.length).fill('')];

        // إعادة إنشاء الجداول
        generateTable();
        generatePatrolTable();
        generateShiftsTable();

        // مسح التخزين المحلي
        localStorage.removeItem('dutyData');
        localStorage.removeItem('patrolData');
        localStorage.removeItem('shiftsData');

        console.log('✅ تم مسح جميع البيانات');
        alert('تم مسح جميع البيانات');
    }
}

function exportToExcel() {
    try {
        // تجميع البيانات للتصدير
        const exportData = {
            dutyData: dutyData,
            patrolData: patrolData,
            shiftsData: shiftsData,
            date: new Date().toISOString()
        };

        // تحويل إلى JSON وتنزيل
        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `كشف_الواجبات_${new Date().toISOString().slice(0, 10)}.json`;
        link.click();

        URL.revokeObjectURL(url);
        console.log('✅ تم تصدير البيانات');
    } catch (error) {
        console.error('❌ خطأ في التصدير:', error);
        alert('حدث خطأ في التصدير');
    }
}

// فتح نافذة البحث عن الأفراد
function openPersonnelSearch() {
    try {
        const modal = document.getElementById('personnelSearchModal');
        if (modal) {
            // استخدام Bootstrap Modal إذا كان متاحاً
            if (typeof bootstrap !== 'undefined') {
                const bsModal = new bootstrap.Modal(modal);
                bsModal.show();
            } else {
                // عرض بديل إذا لم يكن Bootstrap متاحاً
                modal.style.display = 'block';
                modal.classList.add('show');
            }
            console.log('✅ تم فتح نافذة البحث عن الأفراد');
        } else {
            console.error('❌ نافذة البحث عن الأفراد غير موجودة');
            alert('نافذة البحث عن الأفراد غير متاحة');
        }
    } catch (error) {
        console.error('❌ خطأ في فتح نافذة البحث:', error);
        alert('حدث خطأ في فتح نافذة البحث');
    }
}

// البحث المباشر عن الأفراد
function searchPersonnelLive(searchTerm) {
    try {
        if (!searchTerm || searchTerm.length < 2) {
            return;
        }

        const results = personnelDatabase.filter(person =>
            person.name.includes(searchTerm) ||
            person.national_id.includes(searchTerm)
        );

        console.log(`🔍 نتائج البحث عن "${searchTerm}": ${results.length} نتيجة`);

        // عرض النتائج في نافذة البحث
        const resultsContainer = document.getElementById('searchResults');
        if (resultsContainer) {
            resultsContainer.innerHTML = '';
            results.forEach(person => {
                const div = document.createElement('div');
                div.className = 'search-result-item';
                div.innerHTML = `
                    <div class="person-info">
                        <strong>${person.name}</strong><br>
                        <small>الهوية: ${person.national_id}</small>
                    </div>
                    <button class="btn btn-sm btn-primary" onclick="selectPerson(${person.id}, '${person.name}')">
                        اختيار
                    </button>
                `;
                resultsContainer.appendChild(div);
            });
        }

    } catch (error) {
        console.error('❌ خطأ في البحث:', error);
    }
}

// اختيار فرد من نتائج البحث
function selectPerson(personId, personName) {
    try {
        console.log(`✅ تم اختيار الفرد: ${personName} (${personId})`);

        // إغلاق نافذة البحث
        const modal = document.getElementById('personnelSearchModal');
        if (modal) {
            if (typeof bootstrap !== 'undefined') {
                const bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) bsModal.hide();
            } else {
                modal.style.display = 'none';
                modal.classList.remove('show');
            }
        }

        // يمكن إضافة منطق إضافي هنا لإدراج الفرد في الجدول
        alert(`تم اختيار الفرد: ${personName}`);

    } catch (error) {
        console.error('❌ خطأ في اختيار الفرد:', error);
    }
}

// تحميل قاعدة بيانات المواقع
async function loadLocationsDatabase() {
    try {
        console.log('🏢 تحميل قاعدة بيانات المواقع...');
        const response = await fetch('/duties/api/get-locations');
        if (response.ok) {
            const data = await response.json();
            if (data.success && data.locations) {
                locationsDatabase = data.locations;
                console.log(`✅ تم تحميل ${locationsDatabase.length} موقع`);
            }
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل المواقع:', error);
        // استخدام مواقع افتراضية
        locationsDatabase = [
            {id: 1, name: 'البوابة الرئيسية'},
            {id: 2, name: 'البوابة الشرقية'},
            {id: 3, name: 'البوابة الغربية'},
            {id: 4, name: 'مبنى الإدارة'},
            {id: 5, name: 'المستودعات'}
        ];

        // إنشاء خريطة الأفراد للمواقع الافتراضية
        locationPersonnelMap = {
            1: [  // البوابة الرئيسية
                {id: 1, name: 'أحمد محمد', rank: 'رقيب', national_id: '1234567890'},
                {id: 2, name: 'محمد أحمد', rank: 'عريف', national_id: '0987654321'},
                {id: 3, name: 'عبدالله سعد', rank: 'جندي أول', national_id: '1122334455'}
            ],
            2: [  // البوابة الشرقية
                {id: 4, name: 'سعد عبدالله', rank: 'رقيب أول', national_id: '5544332211'},
                {id: 5, name: 'خالد فهد', rank: 'وكيل رقيب', national_id: '6677889900'},
                {id: 6, name: 'فارس علي', rank: 'رقيب', national_id: '1111222233'}
            ],
            3: [  // البوابة الغربية
                {id: 7, name: 'علي فارس', rank: 'عريف', national_id: '3322114455'},
                {id: 8, name: 'محمد فهد', rank: 'جندي', national_id: '5566778899'}
            ],
            4: [  // مبنى الإدارة
                {id: 9, name: 'عبدالرحمن سالم', rank: 'رقيب أول', national_id: '9988776655'},
                {id: 10, name: 'سالم عبدالرحمن', rank: 'وكيل رقيب', national_id: '1357924680'}
            ],
            5: [  // المستودعات
                {id: 1, name: 'أحمد محمد', rank: 'رقيب', national_id: '1234567890'},
                {id: 6, name: 'فارس علي', rank: 'رقيب', national_id: '1111222233'}
            ]
        };

        console.log('✅ تم تحميل المواقع الافتراضية مع خريطة الأفراد');
    }
}

// تحميل قاعدة بيانات الأفراد
async function loadPersonnelDatabase() {
    try {
        console.log('👥 تحميل قاعدة بيانات الأفراد...');
        const response = await fetch('/personnel/api/list');
        if (response.ok) {
            const data = await response.json();
            if (Array.isArray(data)) {
                personnelDatabase = data;
                console.log(`✅ تم تحميل ${personnelDatabase.length} فرد`);
            }
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل الأفراد:', error);
        // استخدام أفراد افتراضيين مع الرتب
        personnelDatabase = [
            {id: 1, name: 'أحمد محمد', rank: 'رقيب', national_id: '1234567890'},
            {id: 2, name: 'محمد أحمد', rank: 'عريف', national_id: '0987654321'},
            {id: 3, name: 'عبدالله سعد', rank: 'جندي أول', national_id: '1122334455'},
            {id: 4, name: 'سعد عبدالله', rank: 'رقيب أول', national_id: '5544332211'},
            {id: 5, name: 'خالد فهد', rank: 'وكيل رقيب', national_id: '6677889900'},
            {id: 6, name: 'فارس علي', rank: 'رقيب', national_id: '1111222233'},
            {id: 7, name: 'علي فارس', rank: 'عريف', national_id: '3322114455'},
            {id: 8, name: 'محمد فهد', rank: 'جندي', national_id: '5566778899'},
            {id: 9, name: 'عبدالرحمن سالم', rank: 'رقيب أول', national_id: '9988776655'},
            {id: 10, name: 'سالم عبدالرحمن', rank: 'وكيل رقيب', national_id: '1357924680'}
        ];
        console.log('✅ تم تحميل الأفراد الافتراضيين مع الرتب');
    }
}

// تهيئة الصفحة - مبسطة لتجنب التعليق
document.addEventListener('DOMContentLoaded', async function() {
    console.log('🚀 بدء تهيئة صفحة كشف الواجبات...');

    try {
        // تحميل البيانات أولاً وانتظارها
        console.log('📡 تحميل قواعد البيانات...');
        await loadLocationsDatabase();

        // فحص وإصلاح هيكل البيانات بعد التحميل
        validateAndFixDataStructure();

        // إضافة أزرار التشخيص
        setTimeout(() => {
            addDiagnosticButtons();
        }, 2000);
        await loadPersonnelDatabase();
        console.log('✅ تم تحميل قواعد البيانات');

        // تهيئة التواريخ أولاً
        initializeDutyReceiptSimple();

        // إنشاء الجداول بعد تحميل البيانات والتواريخ
        console.log('🔨 إنشاء الجداول...');
        generateTable();
        generatePatrolTable();
        generateShiftsTable();

        console.log('✅ تم إنشاء الجداول الأساسية');

        // تهيئة التاريخ الهجري فوراً
        setTimeout(async () => {
            try {
                console.log('📅 تهيئة التاريخ الهجري...');
                const hijriElement = document.getElementById('hijriDate');
                if (hijriElement && !hijriElement.value) {
                    const hijriDate = await getCurrentHijriDate();
                    hijriElement.value = hijriDate;
                    console.log('✅ تم تعيين التاريخ الهجري:', hijriDate);
                }
            } catch (error) {
                console.error('❌ خطأ في تهيئة التاريخ الهجري:', error);
                // استخدام تاريخ هجري احتياطي
                const hijriElement = document.getElementById('hijriDate');
                if (hijriElement && !hijriElement.value) {
                    const fallbackHijri = fallbackHijriCalculation(new Date());
                    hijriElement.value = fallbackHijri;
                    console.log('✅ تم تعيين التاريخ الهجري الاحتياطي:', fallbackHijri);
                }
            }
        }, 800);

        // تحميل المواقع في الخلفية
        setTimeout(async () => {
            try {
                await loadLocations();
                console.log('✅ تم تحميل المواقع وتحديث القوائم');

                // تطبيق المواقع المحفوظة فوراً بعد تحميل قائمة المواقع
                setTimeout(() => {
                    applySelectedLocationsAndPersonnel();
                    applyPatrolLocationsAndPersonnel();
                    applyShiftsLocationsAndPersonnel();
                }, 500);

            } catch (error) {
                console.warn('⚠️ تعذر تحميل المواقع:', error);
                console.log('ℹ️ سيتم استخدام البيانات المحفوظة فقط');
            }
        }, 1000);

        // تحميل البيانات المحفوظة من قاعدة البيانات أولاً
        setTimeout(async () => {
            try {
                console.log('🔄 تحميل البيانات المحفوظة...');
                await loadDataInBackground();
                console.log('✅ تم تحميل البيانات المحفوظة');

                // تطبيق البيانات المحفوظة بشكل متكرر للتأكد
                setTimeout(() => {
                    console.log('🔄 تطبيق أول للبيانات المحفوظة...');
                    applySelectedLocationsAndPersonnel();
                    applyPatrolLocationsAndPersonnel();
                    applyShiftsLocationsAndPersonnel();
                }, 1000);

                setTimeout(() => {
                    console.log('🔄 تطبيق ثاني للبيانات المحفوظة...');
                    applySelectedLocationsAndPersonnel();
                    applyPatrolLocationsAndPersonnel();
                    applyShiftsLocationsAndPersonnel();
                }, 3000);

                setTimeout(() => {
                    console.log('🔄 تطبيق نهائي للبيانات المحفوظة...');
                    applySelectedLocationsAndPersonnel();
                    applyPatrolLocationsAndPersonnel();
                    applyShiftsLocationsAndPersonnel();
                }, 5000);

            } catch (error) {
                console.warn('⚠️ تعذر تحميل البيانات المحفوظة:', error);
            }
        }, 2500);

        // بدء نظام الحفظ التلقائي المحسن
        setTimeout(() => {
            startAutoSave();
            console.log('✅ تم تفعيل نظام الحفظ التلقائي');
        }, 3000);

        // بدء نظام التحميل الدوري للتأكد من استمرارية البيانات
        setTimeout(() => {
            startPeriodicDataLoad();
        }, 10000);

        // إضافة event listeners لحقول النموذج
        setTimeout(() => {
            addFormFieldListeners();
        }, 3500);

        console.log('✅ تم تهيئة الصفحة بنجاح');

    } catch (error) {
        console.error('❌ خطأ في تهيئة الصفحة:', error);
        // إنشاء جداول أساسية حتى لو فشلت التهيئة
        try {
            generateTable();
            generatePatrolTable();
            generateShiftsTable();
        } catch (e) {
            console.error('❌ خطأ في إنشاء الجداول الأساسية:', e);
        }
    }

    // تحديث التواريخ تلقائياً كل دقيقة
    setInterval(updateDatesAutomatically, 60000);

    // تحديث التاريخ الهجري كل ساعة للتأكد من الدقة
    setInterval(async () => {
        const hijriElement = document.getElementById('hijriDate');
        if (hijriElement) {
            const currentHijri = await getCurrentHijriDate();
            hijriElement.value = currentHijri;
            console.log('🕐 تحديث تلقائي للتاريخ الهجري:', currentHijri);
        }
    }, 3600000); // كل ساعة

    // تحديث التاريخ الهجري عند منتصف الليل (تغيير اليوم)
    function scheduleNextMidnightUpdate() {
        const now = new Date();
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);

        const msUntilMidnight = tomorrow.getTime() - now.getTime();

        setTimeout(async () => {
            console.log('🌙 منتصف الليل - تحديث التواريخ...');
            await updateDatesAutomatically();
            scheduleNextMidnightUpdate(); // جدولة التحديث التالي
        }, msUntilMidnight);
    }

    scheduleNextMidnightUpdate();

    console.log('✅ تم تهيئة صفحة كشف الواجبات بنجاح');

    // حفظ البيانات عند إغلاق الصفحة
    window.addEventListener('beforeunload', function() {
        console.log('💾 حفظ أخير قبل إغلاق الصفحة');
        savePersonnelSelections();
        saveDutyDataToServer();
        saveDataToLocalStorage();
    });

    // حفظ دوري كل 30 ثانية للتأكد من عدم فقدان البيانات
    setInterval(() => {
        console.log('🔄 حفظ دوري للبيانات...');
        savePersonnelSelections();
        saveDutyDataToServer();
        savePatrolDataToServer();
        saveShiftsDataToServer();
    }, 30000);

    // مراقب للتغييرات في الجدول
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList' || mutation.type === 'attributes') {
                // التحقق من تغيير في قوائم الأفراد
                const personnelSelects = document.querySelectorAll('.personnel-select');
                personnelSelects.forEach((select, index) => {
                    if (select.value && !select.hasAttribute('data-saved')) {
                        console.log('🔄 اكتشاف تغيير في قائمة الأفراد، حفظ فوري...');
                        select.setAttribute('data-saved', 'true');
                        setTimeout(() => {
                            savePersonnelSelections();
                            saveDutyDataToServer();
                        }, 500);
                    }
                });
            }
        });
    });

    // بدء مراقبة التغييرات لجميع الجداول
    const dutyTable = document.getElementById('dutyTable');
    const patrolTable = document.getElementById('patrolTable');
    const shiftsTable = document.getElementById('shiftsTable');

    if (dutyTable) {
        observer.observe(dutyTable, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['value']
        });
    }

    if (patrolTable) {
        observer.observe(patrolTable, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['value']
        });
    }

    if (shiftsTable) {
        observer.observe(shiftsTable, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['value']
        });
    }
});

// تم استبدال setCurrentDate بـ initializeDutyReceipt

// دوال حفظ واستعادة البيانات عند إضافة صفوف/أعمدة
let tempTableData = null;

// دالة تسجيل محسنة
function debugLog(message, data = null) {
    console.log(`🔧 [DEBUG] ${message}`);
    if (data) {
        console.log('📊 البيانات:', data);
    }
}

function saveCurrentTableData() {
    debugLog('💾 بدء حفظ البيانات الحالية مؤقتاً...');

    // حفظ البيانات مباشرة من مصفوفات البيانات بدلاً من DOM
    tempTableData = {
        dutyData: JSON.parse(JSON.stringify(dutyData)),
        patrolData: JSON.parse(JSON.stringify(patrolData)),
        shiftsData: JSON.parse(JSON.stringify(shiftsData)),
        domData: {
            locations: {},
            personnel: {},
            textInputs: {},
            patrolLocations: {},
            patrolPersonnel: {},
            shiftsLocations: {},
            shiftsPersonnel: {}
        }
    };

    debugLog('📋 تم حفظ مصفوفات البيانات', {
        dutyRows: tempTableData.dutyData.rows.length,
        patrolRows: tempTableData.patrolData.rows.length,
        shiftsRows: tempTableData.shiftsData.rows.length
    });

    // حفظ البيانات من DOM كنسخة احتياطية
    const mainTable = document.getElementById('dutyTable');
    if (mainTable) {
        const rows = mainTable.querySelectorAll('tbody tr');

        // التأكد من أن مصفوفة البيانات تحتوي على عدد كافٍ من الصفوف
        while (dutyData.rows.length < rows.length) {
            dutyData.rows.push(Array(dutyData.headers.length).fill(''));
        }

        rows.forEach((row, rowIndex) => {
            // التأكد من وجود الصف في مصفوفة البيانات
            if (!dutyData.rows[rowIndex]) {
                dutyData.rows[rowIndex] = Array(dutyData.headers.length).fill('');
            }

            // حفظ المواقع
            const locationSelect = row.querySelector('.location-select');
            if (locationSelect && locationSelect.value) {
                tempTableData.domData.locations[rowIndex] = locationSelect.value;
                // تحديث مصفوفة البيانات أيضاً
                dutyData.rows[rowIndex][1] = locationSelect.value;
                debugLog(`💾 حفظ موقع للصف ${rowIndex}: ${locationSelect.value}`);
            }

            // حفظ الأفراد
            const personnelSelects = row.querySelectorAll('.personnel-select');
            personnelSelects.forEach((select, colIndex) => {
                if (select.value) {
                    tempTableData.domData.personnel[`${rowIndex}-${colIndex}`] = select.value;
                    // تحديث مصفوفة البيانات
                    const actualColIndex = colIndex + 2; // لأن الأفراد يبدأون من العمود 2
                    if (actualColIndex < dutyData.rows[rowIndex].length) {
                        dutyData.rows[rowIndex][actualColIndex] = select.value;
                        debugLog(`💾 حفظ فرد للصف ${rowIndex}, العمود ${actualColIndex}: ${select.value}`);
                    }
                }
            });

            // حفظ النصوص والملاحظات
            const textInputs = row.querySelectorAll('input[type="text"], textarea');
            textInputs.forEach((input, colIndex) => {
                if (input.value) {
                    tempTableData.domData.textInputs[`${rowIndex}-${colIndex}`] = input.value;
                    // تحديث مصفوفة البيانات للملاحظات (العمود الأخير عادة)
                    const notesColIndex = dutyData.headers.length - 1;
                    if (colIndex === 0) { // أول input text عادة هو الملاحظات
                        dutyData.rows[rowIndex][notesColIndex] = input.value;
                        debugLog(`💾 حفظ ملاحظة للصف ${rowIndex}: ${input.value}`);
                    }
                }
            });
        });
    }

    // حفظ بيانات جدول الدوريات
    const patrolTable = document.getElementById('patrolTable');
    if (patrolTable) {
        const rows = patrolTable.querySelectorAll('tbody tr');
        rows.forEach((row, rowIndex) => {
            const locationSelect = row.querySelector('.location-select');
            if (locationSelect && locationSelect.value) {
                tempTableData.domData.patrolLocations[rowIndex] = locationSelect.value;
            }

            const personnelSelects = row.querySelectorAll('.personnel-select');
            personnelSelects.forEach((select, colIndex) => {
                if (select.value) {
                    tempTableData.domData.patrolPersonnel[`${rowIndex}-${colIndex}`] = select.value;
                }
            });
        });
    }

    // حفظ بيانات جدول المناوبين
    const shiftsTable = document.getElementById('shiftsTable');
    if (shiftsTable) {
        const rows = shiftsTable.querySelectorAll('tbody tr');
        rows.forEach((row, rowIndex) => {
            const locationSelect = row.querySelector('.location-select');
            if (locationSelect && locationSelect.value) {
                tempTableData.domData.shiftsLocations[rowIndex] = locationSelect.value;
            }

            const personnelSelects = row.querySelectorAll('.personnel-select');
            personnelSelects.forEach((select, colIndex) => {
                if (select.value) {
                    tempTableData.domData.shiftsPersonnel[`${rowIndex}-${colIndex}`] = select.value;
                }
            });
        });
    }

    console.log('✅ تم حفظ البيانات مؤقتاً:', tempTableData);
}

function restoreTableData() {
    if (!tempTableData) {
        console.log('⚠️ لا توجد بيانات مؤقتة للاستعادة');
        return;
    }

    console.log('🔄 استعادة البيانات المحفوظة...');

    // استعادة مصفوفات البيانات أولاً
    if (tempTableData.dutyData) {
        dutyData.rows = JSON.parse(JSON.stringify(tempTableData.dutyData.rows));
        console.log('✅ تم استعادة بيانات الجدول الرئيسي من المصفوفة');
    }

    if (tempTableData.patrolData) {
        patrolData.rows = JSON.parse(JSON.stringify(tempTableData.patrolData.rows));
        console.log('✅ تم استعادة بيانات الدوريات من المصفوفة');
    }

    if (tempTableData.shiftsData) {
        shiftsData.rows = JSON.parse(JSON.stringify(tempTableData.shiftsData.rows));
        console.log('✅ تم استعادة بيانات المناوبين من المصفوفة');
    }

    // استعادة البيانات في DOM
    setTimeout(() => {
        restoreDOMData();

        // تطبيق البيانات المحفوظة على الجداول
        setTimeout(() => {
            applySelectedLocationsAndPersonnel();
            applyPatrolLocationsAndPersonnel();
            applyShiftsLocationsAndPersonnel();
        }, 500);
    }, 200);

    console.log('✅ تم استعادة البيانات');
}

// دالة استعادة مبسطة وأكثر فعالية
function restoreTableDataSimple() {
    if (!tempTableData || !tempTableData.dutyData) {
        debugLog('⚠️ لا توجد بيانات للاستعادة');
        return;
    }

    debugLog('🔄 بدء الاستعادة المبسطة...');

    // استعادة البيانات من المصفوفة المحفوظة
    const savedRows = tempTableData.dutyData.rows;

    // تطبيق البيانات على الجدول الحالي
    const mainTable = document.getElementById('dutyTable');
    if (mainTable) {
        const rows = mainTable.querySelectorAll('tbody tr');

        rows.forEach((row, rowIndex) => {
            if (savedRows[rowIndex]) {
                // استعادة الموقع
                const locationValue = savedRows[rowIndex][1];
                if (locationValue && locationValue !== '') {
                    const locationSelect = row.querySelector('.location-select');
                    if (locationSelect) {
                        locationSelect.value = locationValue;
                        debugLog(`📍 استعادة موقع للصف ${rowIndex}: ${locationValue}`);

                        // تحميل أفراد الموقع
                        const location = locationsDatabase?.find(loc => loc.name === locationValue);
                        if (location) {
                            setTimeout(() => {
                                loadPersonnelForLocation(location.id, rowIndex);

                                // استعادة الأفراد بعد تحميل القائمة
                                setTimeout(() => {
                                    const personnelSelects = row.querySelectorAll('.personnel-select');
                                    personnelSelects.forEach((select, colIndex) => {
                                        const actualColIndex = colIndex + 2;
                                        const personnelValue = savedRows[rowIndex][actualColIndex];
                                        if (personnelValue && personnelValue !== '') {
                                            select.value = personnelValue;
                                            debugLog(`👤 استعادة فرد للصف ${rowIndex}, العمود ${colIndex}: ${personnelValue}`);
                                        }
                                    });
                                }, 500);
                            }, 200);
                        }
                    }
                }

                // استعادة الملاحظات
                const notesColIndex = dutyData.headers.length - 1;
                const notesValue = savedRows[rowIndex][notesColIndex];
                if (notesValue && notesValue !== '') {
                    const textInput = row.querySelector('input[type="text"], textarea');
                    if (textInput) {
                        textInput.value = notesValue;
                        debugLog(`📝 استعادة ملاحظة للصف ${rowIndex}: ${notesValue}`);
                    }
                }
            }
        });
    }

    debugLog('✅ تم إكمال الاستعادة المبسطة');
}

// دالة استعادة البيانات مع مراعاة العمود الجديد
function restoreTableDataWithNewColumn(newColumnIndex) {
    if (!tempTableData || !tempTableData.dutyData) {
        debugLog('⚠️ لا توجد بيانات للاستعادة');
        return;
    }

    debugLog(`🔄 بدء الاستعادة مع العمود الجديد في الموضع ${newColumnIndex}...`);

    // استعادة البيانات من المصفوفة المحفوظة
    const savedRows = tempTableData.dutyData.rows;

    // تطبيق البيانات على الجدول الحالي مع تعديل الفهارس
    const mainTable = document.getElementById('dutyTable');
    if (mainTable) {
        const rows = mainTable.querySelectorAll('tbody tr');

        rows.forEach((row, rowIndex) => {
            if (savedRows[rowIndex]) {
                // استعادة الموقع (العمود 1 لا يتأثر)
                const locationValue = savedRows[rowIndex][1];
                if (locationValue && locationValue !== '') {
                    const locationSelect = row.querySelector('.location-select');
                    if (locationSelect) {
                        locationSelect.value = locationValue;
                        debugLog(`📍 استعادة موقع للصف ${rowIndex}: ${locationValue}`);

                        // تحميل أفراد الموقع
                        const location = locationsDatabase?.find(loc => loc.name === locationValue);
                        if (location) {
                            setTimeout(() => {
                                loadPersonnelForLocation(location.id, rowIndex);

                                // استعادة الأفراد مع تعديل الفهارس
                                setTimeout(() => {
                                    const personnelSelects = row.querySelectorAll('.personnel-select');
                                    personnelSelects.forEach((select, colIndex) => {
                                        // حساب الفهرس الأصلي مع مراعاة العمود الجديد
                                        let originalColIndex = colIndex + 2;
                                        if (originalColIndex >= newColumnIndex) {
                                            originalColIndex = originalColIndex - 1; // العمود كان قبل إضافة العمود الجديد
                                        }

                                        const personnelValue = savedRows[rowIndex][originalColIndex];
                                        if (personnelValue && personnelValue !== '') {
                                            select.value = personnelValue;
                                            debugLog(`👤 استعادة فرد للصف ${rowIndex}, العمود ${colIndex}: ${personnelValue}`);
                                        }
                                    });
                                }, 500);
                            }, 200);
                        }
                    }
                }

                // استعادة الملاحظات (العمود الأخير في البيانات المحفوظة)
                const savedNotesColIndex = tempTableData.dutyData.headers.length - 1;
                const notesValue = savedRows[rowIndex][savedNotesColIndex];
                if (notesValue && notesValue !== '') {
                    const textInput = row.querySelector('input[type="text"], textarea');
                    if (textInput) {
                        textInput.value = notesValue;
                        debugLog(`📝 استعادة ملاحظة للصف ${rowIndex}: ${notesValue}`);
                    }
                }
            }
        });
    }

    debugLog('✅ تم إكمال الاستعادة مع العمود الجديد');
}

// دوال حفظ واستعادة مخصصة للدوريات
let tempPatrolData = null;

function saveCurrentPatrolData() {
    debugLog('💾 حفظ بيانات الدوريات الحالية...');

    tempPatrolData = {
        patrolData: JSON.parse(JSON.stringify(patrolData)),
        domData: {
            locations: {},
            personnel: {}
        }
    };

    const patrolTable = document.getElementById('patrolTable');
    if (patrolTable) {
        const rows = patrolTable.querySelectorAll('tbody tr');

        // التأكد من أن مصفوفة البيانات تحتوي على عدد كافٍ من الصفوف
        while (patrolData.rows.length < rows.length) {
            patrolData.rows.push(Array(patrolData.headers.length).fill(''));
        }

        rows.forEach((row, rowIndex) => {
            // التأكد من وجود الصف
            if (!patrolData.rows[rowIndex]) {
                patrolData.rows[rowIndex] = Array(patrolData.headers.length).fill('');
            }

            // حفظ المواقع
            const locationSelect = row.querySelector('.location-select');
            if (locationSelect && locationSelect.value) {
                tempPatrolData.domData.locations[rowIndex] = locationSelect.value;
                patrolData.rows[rowIndex][1] = locationSelect.value;
                debugLog(`💾 حفظ موقع دورية للصف ${rowIndex}: ${locationSelect.value}`);
            }

            // حفظ الأفراد
            const personnelSelects = row.querySelectorAll('.personnel-select');
            personnelSelects.forEach((select, colIndex) => {
                if (select.value) {
                    tempPatrolData.domData.personnel[`${rowIndex}-${colIndex}`] = select.value;
                    const actualColIndex = colIndex + 2;
                    if (actualColIndex < patrolData.rows[rowIndex].length) {
                        patrolData.rows[rowIndex][actualColIndex] = select.value;
                        debugLog(`💾 حفظ فرد دورية للصف ${rowIndex}, العمود ${actualColIndex}: ${select.value}`);
                    }
                }
            });
        });
    }
}

function restorePatrolDataSimple() {
    if (!tempPatrolData || !tempPatrolData.patrolData) {
        debugLog('⚠️ لا توجد بيانات دوريات للاستعادة');
        return;
    }

    debugLog('🔄 بدء استعادة بيانات الدوريات...');

    const savedRows = tempPatrolData.patrolData.rows;
    const patrolTable = document.getElementById('patrolTable');

    if (patrolTable) {
        const rows = patrolTable.querySelectorAll('tbody tr');

        rows.forEach((row, rowIndex) => {
            if (savedRows[rowIndex]) {
                // استعادة الموقع
                const locationValue = savedRows[rowIndex][1];
                if (locationValue && locationValue !== '') {
                    const locationSelect = row.querySelector('.location-select');
                    if (locationSelect) {
                        locationSelect.value = locationValue;
                        debugLog(`📍 استعادة موقع دورية للصف ${rowIndex}: ${locationValue}`);

                        // تحميل أفراد الموقع
                        const location = locationsDatabase?.find(loc => loc.name === locationValue);
                        if (location) {
                            setTimeout(() => {
                                loadPersonnelForLocation(location.id, rowIndex);

                                // استعادة الأفراد
                                setTimeout(() => {
                                    const personnelSelects = row.querySelectorAll('.personnel-select');
                                    personnelSelects.forEach((select, colIndex) => {
                                        const actualColIndex = colIndex + 2;
                                        const personnelValue = savedRows[rowIndex][actualColIndex];
                                        if (personnelValue && personnelValue !== '') {
                                            select.value = personnelValue;
                                            debugLog(`👤 استعادة فرد دورية للصف ${rowIndex}, العمود ${colIndex}: ${personnelValue}`);
                                        }
                                    });
                                }, 500);
                            }, 200);
                        }
                    }
                }
            }
        });
    }

    debugLog('✅ تم إكمال استعادة بيانات الدوريات');
}

// دوال حفظ واستعادة مخصصة للمناوبين
let tempShiftsData = null;

function saveCurrentShiftsData() {
    debugLog('💾 حفظ بيانات المناوبين الحالية...');

    tempShiftsData = {
        shiftsData: JSON.parse(JSON.stringify(shiftsData)),
        domData: {
            locations: {},
            personnel: {}
        }
    };

    const shiftsTable = document.getElementById('shiftsTable');
    if (shiftsTable) {
        const rows = shiftsTable.querySelectorAll('tbody tr');

        // التأكد من أن مصفوفة البيانات تحتوي على عدد كافٍ من الصفوف
        while (shiftsData.rows.length < rows.length) {
            shiftsData.rows.push(Array(shiftsData.headers.length).fill(''));
        }

        rows.forEach((row, rowIndex) => {
            // التأكد من وجود الصف
            if (!shiftsData.rows[rowIndex]) {
                shiftsData.rows[rowIndex] = Array(shiftsData.headers.length).fill('');
            }

            // حفظ المواقع
            const locationSelect = row.querySelector('.location-select');
            if (locationSelect && locationSelect.value) {
                tempShiftsData.domData.locations[rowIndex] = locationSelect.value;
                shiftsData.rows[rowIndex][1] = locationSelect.value;
                debugLog(`💾 حفظ موقع مناوبة للصف ${rowIndex}: ${locationSelect.value}`);
            }

            // حفظ الأفراد
            const personnelSelects = row.querySelectorAll('.personnel-select');
            personnelSelects.forEach((select, colIndex) => {
                if (select.value) {
                    tempShiftsData.domData.personnel[`${rowIndex}-${colIndex}`] = select.value;
                    const actualColIndex = colIndex + 2;
                    if (actualColIndex < shiftsData.rows[rowIndex].length) {
                        shiftsData.rows[rowIndex][actualColIndex] = select.value;
                        debugLog(`💾 حفظ فرد مناوبة للصف ${rowIndex}, العمود ${actualColIndex}: ${select.value}`);
                    }
                }
            });
        });
    }
}

function restoreShiftsDataSimple() {
    if (!tempShiftsData || !tempShiftsData.shiftsData) {
        debugLog('⚠️ لا توجد بيانات مناوبين للاستعادة');
        return;
    }

    debugLog('🔄 بدء استعادة بيانات المناوبين...');

    const savedRows = tempShiftsData.shiftsData.rows;
    const shiftsTable = document.getElementById('shiftsTable');

    if (shiftsTable) {
        const rows = shiftsTable.querySelectorAll('tbody tr');

        rows.forEach((row, rowIndex) => {
            if (savedRows[rowIndex]) {
                // استعادة الموقع
                const locationValue = savedRows[rowIndex][1];
                if (locationValue && locationValue !== '') {
                    const locationSelect = row.querySelector('.location-select');
                    if (locationSelect) {
                        locationSelect.value = locationValue;
                        debugLog(`📍 استعادة موقع مناوبة للصف ${rowIndex}: ${locationValue}`);

                        // تحميل أفراد الموقع
                        const location = locationsDatabase?.find(loc => loc.name === locationValue);
                        if (location) {
                            setTimeout(() => {
                                loadPersonnelForLocation(location.id, rowIndex);

                                // استعادة الأفراد
                                setTimeout(() => {
                                    const personnelSelects = row.querySelectorAll('.personnel-select');
                                    personnelSelects.forEach((select, colIndex) => {
                                        const actualColIndex = colIndex + 2;
                                        const personnelValue = savedRows[rowIndex][actualColIndex];
                                        if (personnelValue && personnelValue !== '') {
                                            select.value = personnelValue;
                                            debugLog(`👤 استعادة فرد مناوبة للصف ${rowIndex}, العمود ${colIndex}: ${personnelValue}`);
                                        }
                                    });
                                }, 500);
                            }, 200);
                        }
                    }
                }
            }
        });
    }

    debugLog('✅ تم إكمال استعادة بيانات المناوبين');
}

function restoreDOMData() {
    if (!tempTableData || !tempTableData.domData) return;

    console.log('🔄 استعادة بيانات DOM...');

    // استعادة بيانات الجدول الرئيسي
    const mainTable = document.getElementById('dutyTable');
    if (mainTable) {
        const rows = mainTable.querySelectorAll('tbody tr');
        rows.forEach((row, rowIndex) => {
            // استعادة المواقع
            if (tempTableData.domData.locations[rowIndex]) {
                const locationSelect = row.querySelector('.location-select');
                if (locationSelect) {
                    locationSelect.value = tempTableData.domData.locations[rowIndex];
                    console.log(`📍 استعادة موقع للصف ${rowIndex}: ${tempTableData.domData.locations[rowIndex]}`);

                    // تحميل أفراد الموقع
                    const location = locationsDatabase?.find(loc => loc.name === tempTableData.domData.locations[rowIndex]);
                    if (location) {
                        loadPersonnelForLocation(location.id, rowIndex);
                    }
                }
            }

            // استعادة الأفراد
            const personnelSelects = row.querySelectorAll('.personnel-select');
            personnelSelects.forEach((select, colIndex) => {
                const key = `${rowIndex}-${colIndex}`;
                if (tempTableData.domData.personnel[key]) {
                    setTimeout(() => {
                        select.value = tempTableData.domData.personnel[key];
                        console.log(`👤 استعادة فرد للصف ${rowIndex}, العمود ${colIndex}: ${tempTableData.domData.personnel[key]}`);
                    }, 800);
                }
            });

            // استعادة النصوص
            const textInputs = row.querySelectorAll('input[type="text"], textarea');
            textInputs.forEach((input, colIndex) => {
                const key = `${rowIndex}-${colIndex}`;
                if (tempTableData.domData.textInputs[key]) {
                    input.value = tempTableData.domData.textInputs[key];
                    console.log(`📝 استعادة نص للصف ${rowIndex}, العمود ${colIndex}: ${tempTableData.domData.textInputs[key]}`);
                }
            });
        });
    }

    // استعادة بيانات الدوريات والمناوبين
    setTimeout(() => {
        restorePatrolAndShiftsData();
    }, 1000);
}

function restorePatrolAndShiftsData() {
    if (!tempTableData || !tempTableData.domData) return;

    // استعادة بيانات الدوريات
    const patrolTable = document.getElementById('patrolTable');
    if (patrolTable) {
        const rows = patrolTable.querySelectorAll('tbody tr');
        rows.forEach((row, rowIndex) => {
            if (tempTableData.domData.patrolLocations[rowIndex]) {
                const locationSelect = row.querySelector('.location-select');
                if (locationSelect) {
                    locationSelect.value = tempTableData.domData.patrolLocations[rowIndex];
                }
            }

            const personnelSelects = row.querySelectorAll('.personnel-select');
            personnelSelects.forEach((select, colIndex) => {
                const key = `${rowIndex}-${colIndex}`;
                if (tempTableData.domData.patrolPersonnel[key]) {
                    setTimeout(() => {
                        select.value = tempTableData.domData.patrolPersonnel[key];
                    }, 300);
                }
            });
        });
    }

    // استعادة بيانات المناوبين
    const shiftsTable = document.getElementById('shiftsTable');
    if (shiftsTable) {
        const rows = shiftsTable.querySelectorAll('tbody tr');
        rows.forEach((row, rowIndex) => {
            if (tempTableData.domData.shiftsLocations[rowIndex]) {
                const locationSelect = row.querySelector('.location-select');
                if (locationSelect) {
                    locationSelect.value = tempTableData.domData.shiftsLocations[rowIndex];
                }
            }

            const personnelSelects = row.querySelectorAll('.personnel-select');
            personnelSelects.forEach((select, colIndex) => {
                const key = `${rowIndex}-${colIndex}`;
                if (tempTableData.domData.shiftsPersonnel[key]) {
                    setTimeout(() => {
                        select.value = tempTableData.domData.shiftsPersonnel[key];
                    }, 300);
                }
            });
        });
    }
}

// دوال تطبيق البيانات المحفوظة على الجداول
function applySelectedLocationsAndPersonnel() {
    if (!dutyData || !dutyData.rows) return;

    console.log('🔄 تطبيق المواقع والأفراد المحفوظة على الجدول الرئيسي...');

    const mainTable = document.getElementById('dutyTable');
    if (mainTable) {
        const rows = mainTable.querySelectorAll('tbody tr');
        rows.forEach((row, rowIndex) => {
            if (dutyData.rows[rowIndex]) {
                // تطبيق الموقع المحفوظ
                const locationValue = dutyData.rows[rowIndex][1];
                if (locationValue) {
                    const locationSelect = row.querySelector('.location-select');
                    if (locationSelect) {
                        locationSelect.value = locationValue;
                        console.log(`📍 تطبيق موقع محفوظ: ${locationValue}`);

                        // تحميل أفراد الموقع
                        const location = locationsDatabase?.find(loc => loc.name === locationValue);
                        if (location) {
                            setTimeout(() => {
                                loadPersonnelForLocation(location.id, rowIndex);

                                // تطبيق الأفراد المحفوظين بعد تحميل القائمة
                                setTimeout(() => {
                                    const personnelSelects = row.querySelectorAll('.personnel-select');
                                    personnelSelects.forEach((select, colIndex) => {
                                        const actualColIndex = colIndex + 2;
                                        const personnelValue = dutyData.rows[rowIndex][actualColIndex];
                                        if (personnelValue) {
                                            select.value = personnelValue;
                                            console.log(`👤 تطبيق فرد محفوظ: ${personnelValue}`);
                                        }
                                    });
                                }, 500);
                            }, 200);
                        }
                    }
                }

                // تطبيق الملاحظات المحفوظة
                const notesColIndex = dutyData.headers.length - 1;
                const notesValue = dutyData.rows[rowIndex][notesColIndex];
                if (notesValue) {
                    const textInput = row.querySelector('input[type="text"], textarea');
                    if (textInput) {
                        textInput.value = notesValue;
                        console.log(`📝 تطبيق ملاحظة محفوظة: ${notesValue}`);
                    }
                }
            }
        });
    }
}

function applyPatrolLocationsAndPersonnel() {
    if (!patrolData || !patrolData.rows) return;

    console.log('🔄 تطبيق المواقع والأفراد المحفوظة على جدول الدوريات...');

    const patrolTable = document.getElementById('patrolTable');
    if (patrolTable) {
        const rows = patrolTable.querySelectorAll('tbody tr');
        rows.forEach((row, rowIndex) => {
            if (patrolData.rows[rowIndex]) {
                const locationValue = patrolData.rows[rowIndex][1];
                if (locationValue) {
                    const locationSelect = row.querySelector('.location-select');
                    if (locationSelect) {
                        locationSelect.value = locationValue;

                        const location = locationsDatabase?.find(loc => loc.name === locationValue);
                        if (location) {
                            setTimeout(() => {
                                loadPersonnelForLocation(location.id, rowIndex);

                                setTimeout(() => {
                                    const personnelSelects = row.querySelectorAll('.personnel-select');
                                    personnelSelects.forEach((select, colIndex) => {
                                        const actualColIndex = colIndex + 2;
                                        const personnelValue = patrolData.rows[rowIndex][actualColIndex];
                                        if (personnelValue) {
                                            select.value = personnelValue;
                                        }
                                    });
                                }, 500);
                            }, 200);
                        }
                    }
                }
            }
        });
    }
}

function applyShiftsLocationsAndPersonnel() {
    if (!shiftsData || !shiftsData.rows) return;

    console.log('🔄 تطبيق المواقع والأفراد المحفوظة على جدول المناوبين...');

    const shiftsTable = document.getElementById('shiftsTable');
    if (shiftsTable) {
        const rows = shiftsTable.querySelectorAll('tbody tr');
        rows.forEach((row, rowIndex) => {
            if (shiftsData.rows[rowIndex]) {
                const locationValue = shiftsData.rows[rowIndex][1];
                if (locationValue) {
                    const locationSelect = row.querySelector('.location-select');
                    if (locationSelect) {
                        locationSelect.value = locationValue;

                        const location = locationsDatabase?.find(loc => loc.name === locationValue);
                        if (location) {
                            setTimeout(() => {
                                loadPersonnelForLocation(location.id, rowIndex);

                                setTimeout(() => {
                                    const personnelSelects = row.querySelectorAll('.personnel-select');
                                    personnelSelects.forEach((select, colIndex) => {
                                        const actualColIndex = colIndex + 2;
                                        const personnelValue = shiftsData.rows[rowIndex][actualColIndex];
                                        if (personnelValue) {
                                            select.value = personnelValue;
                                        }
                                    });
                                }, 500);
                            }, 200);
                        }
                    }
                }
            }
        });
    }
}

// دوال إضافة الصفوف والأعمدة

// إضافة صف جديد للجدول الرئيسي
function addRow() {
    console.log('🔄 إضافة صف جديد للجدول الرئيسي...');

    // حفظ البيانات الحالية أولاً
    saveCurrentTableData();

    // إضافة الصف الجديد
    const newRow = Array(dutyData.headers.length).fill('');
    dutyData.rows.push(newRow);

    // إعادة إنشاء الجدول
    generateTable();

    // استعادة البيانات بعد إعادة الإنشاء
    setTimeout(() => {
        restoreTableData();
        saveDutyDataToServer();
        console.log('✅ تم إضافة الصف الجديد مع الحفاظ على البيانات');
    }, 300);
}

// إضافة صف بعد صف محدد - نسخة مبسطة وفعالة
function addRowAfter(rowIndex) {
    console.log('🔄 إضافة صف جديد...');

    try {
        // 1. حفظ البيانات الحالية أولاً
        saveCurrentTableData();

        // 2. إضافة الصف الجديد للمصفوفة مباشرة
        const newRow = Array(dutyData.headers.length).fill('');
        dutyData.rows.splice(rowIndex + 1, 0, newRow);
        console.log(`➕ تم إضافة صف جديد في الموضع ${rowIndex + 1}`);

        // 3. إعادة إنشاء الجدول
        generateTable();
        console.log('🔄 تم إعادة إنشاء الجدول');

        // 4. استعادة البيانات وحفظ
        setTimeout(() => {
            restoreTableData();
            saveDutyDataToServer();
            console.log('✅ تم إضافة الصف وحفظه مع الحفاظ على البيانات');
        }, 300);

    } catch (error) {
        console.error('❌ خطأ في إضافة الصف:', error);
        alert('حدث خطأ في إضافة الصف. يرجى المحاولة مرة أخرى.');
    }
}

// دالة استخراج البيانات الحالية من DOM
function extractCurrentTableData() {
    const data = [];
    const mainTable = document.getElementById('dutyTable');

    if (mainTable) {
        const rows = mainTable.querySelectorAll('tbody tr');
        rows.forEach((row, rowIndex) => {
            const rowData = [];

            // استخراج رقم الصف
            rowData[0] = rowIndex + 1;

            // استخراج الموقع
            const locationSelect = row.querySelector('.location-select');
            rowData[1] = locationSelect ? locationSelect.value : '';

            // استخراج الأفراد
            const personnelSelects = row.querySelectorAll('.personnel-select');
            personnelSelects.forEach((select, colIndex) => {
                rowData[colIndex + 2] = select.value || '';
            });

            // استخراج الملاحظات
            const textInputs = row.querySelectorAll('input[type="text"], textarea');
            if (textInputs.length > 0) {
                rowData[dutyData.headers.length - 1] = textInputs[0].value || '';
            }

            data[rowIndex] = rowData;
        });
    }

    return data;
}

// دالة دمج البيانات المحفوظة مع المصفوفة الجديدة
function mergeDataIntoArray(savedData, targetArray) {
    savedData.forEach((rowData, index) => {
        if (targetArray[index]) {
            rowData.forEach((cellData, cellIndex) => {
                if (cellData && cellData !== '') {
                    targetArray[index][cellIndex] = cellData;
                }
            });
        }
    });
    debugLog('🔄 تم دمج البيانات المحفوظة مع المصفوفة الجديدة');
}

// دالة تطبيق البيانات على الجدول
function applyDataToTable(dataArray) {
    const mainTable = document.getElementById('dutyTable');

    if (mainTable) {
        const rows = mainTable.querySelectorAll('tbody tr');
        rows.forEach((row, rowIndex) => {
            if (dataArray[rowIndex]) {
                // تطبيق الموقع
                const locationValue = dataArray[rowIndex][1];
                if (locationValue) {
                    const locationSelect = row.querySelector('.location-select');
                    if (locationSelect) {
                        locationSelect.value = locationValue;
                        debugLog(`📍 تطبيق موقع: ${locationValue} للصف ${rowIndex}`);

                        // تحميل أفراد الموقع
                        const location = locationsDatabase?.find(loc => loc.name === locationValue);
                        if (location) {
                            setTimeout(() => {
                                loadPersonnelForLocation(location.id, rowIndex);

                                // تطبيق الأفراد بعد تحميل القائمة
                                setTimeout(() => {
                                    const personnelSelects = row.querySelectorAll('.personnel-select');
                                    personnelSelects.forEach((select, colIndex) => {
                                        const personnelValue = dataArray[rowIndex][colIndex + 2];
                                        if (personnelValue) {
                                            select.value = personnelValue;
                                            debugLog(`👤 تطبيق فرد: ${personnelValue} للصف ${rowIndex}, العمود ${colIndex}`);
                                        }
                                    });
                                }, 300);
                            }, 200);
                        }
                    }
                }

                // تطبيق الملاحظات
                const notesValue = dataArray[rowIndex][dutyData.headers.length - 1];
                if (notesValue) {
                    const textInput = row.querySelector('input[type="text"], textarea');
                    if (textInput) {
                        textInput.value = notesValue;
                        debugLog(`📝 تطبيق ملاحظة: ${notesValue} للصف ${rowIndex}`);
                    }
                }
            }
        });
    }
}

// حذف صف
function deleteRow(rowIndex) {
    // فحص وإصلاح البيانات أولاً
    validateAndFixDataStructure();

    console.log('🔍 فحص إمكانية الحذف:', {
        'عدد الصفوف الحالي': dutyData.rows.length,
        'فهرس الصف المراد حذفه': rowIndex,
        'نوع البيانات': typeof dutyData.rows,
        'البيانات صحيحة': Array.isArray(dutyData.rows),
        'محتوى أول صف': dutyData.rows[0] ? dutyData.rows[0].length : 'غير موجود'
    });

    // إظهار حالة البيانات المفصلة
    showDataStatus();

    // إذا كانت البيانات فارغة، حاول إعادة التحميل
    if (dutyData.rows.length === 0) {
        console.warn('⚠️ البيانات فارغة، محاولة إعادة التحميل...');
        loadAllDataFromServer();
        setTimeout(() => {
            if (dutyData.rows.length === 0) {
                alert('لا توجد بيانات للحذف. يرجى إعادة تحميل الصفحة.');
                return;
            }
        }, 1000);
        return;
    }

    if (dutyData.rows.length > 1) {
        if (confirm('هل أنت متأكد من حذف هذا الصف؟')) {
            console.log(`🗑️ حذف الصف ${rowIndex + 1} من الجدول الرئيسي`);

            // حذف الصف مباشرة
            dutyData.rows.splice(rowIndex, 1);

            // إعادة إنشاء الجدول
            generateTable();

            // حفظ البيانات المحدثة فوراً وتحديث القوائم
            setTimeout(async () => {
                // حفظ فوري ودائم في جميع الأنظمة
                const saved = await saveDeletedDataPermanently();

                if (saved) {
                    // تحديث قوائم المواقع والأفراد لإزالة القيود
                    refreshAllLocationSelects();
                    refreshAllPersonnelSelects();

                    // إشعار المستخدم بنجاح الحذف
                    if (typeof showNotification === 'function') {
                        showNotification('✅ تم حذف الصف وحفظ التغييرات نهائياً', 'success');
                    }

                    console.log('✅ تم حذف الصف وحفظ البيانات المحدثة نهائياً');
                } else {
                    console.error('❌ فشل في حفظ البيانات المحذوفة');
                    if (typeof showNotification === 'function') {
                        showNotification('❌ فشل في حفظ التغييرات', 'error');
                    }
                }
            }, 100);
        }
    } else {
        console.error('❌ منع الحذف:', {
            'عدد الصفوف': dutyData.rows.length,
            'السبب': 'عدد الصفوف أقل من أو يساوي 1',
            'محتوى البيانات': dutyData.rows
        });

        // محاولة إعادة تحميل البيانات
        const shouldReload = confirm(`لا يمكن حذف الصف الوحيد المتبقي\nعدد الصفوف الحالي: ${dutyData.rows.length}\n\nهل تريد إعادة تحميل البيانات من الخادم؟`);
        if (shouldReload) {
            console.log('🔄 إعادة تحميل البيانات من الخادم...');
            loadAllDataFromServer().then(() => {
                generateTable();
                console.log('✅ تم إعادة تحميل البيانات');
            });
        }
    }
}

// إضافة عمود بعد عمود محدد - نسخة مبسطة
function addColumnAfter(columnIndex) {
    console.log('🔄 إضافة عمود جديد...');

    try {
        // 1. إضافة العمود الجديد للرأس والصفوف مباشرة
        dutyData.headers.splice(columnIndex + 1, 0, 'عمود جديد');
        dutyData.rows.forEach(row => {
            row.splice(columnIndex + 1, 0, '');
        });
        console.log(`➕ تم إضافة عمود جديد في الموضع ${columnIndex + 1}`);

        // 2. إعادة إنشاء الجدول
        generateTable();
        console.log('🔄 تم إعادة إنشاء الجدول');

        // 3. حفظ في قاعدة البيانات
        setTimeout(() => {
            saveDutyDataToServer();
            console.log('✅ تم إضافة العمود وحفظه في قاعدة البيانات');
        }, 500);

    } catch (error) {
        console.error('❌ خطأ في إضافة العمود:', error);
        alert('حدث خطأ في إضافة العمود. يرجى المحاولة مرة أخرى.');
    }
}

// دالة دمج البيانات مع مراعاة العمود الجديد
function mergeDataIntoArrayWithNewColumn(savedData, targetArray, newColumnIndex) {
    savedData.forEach((rowData, rowIndex) => {
        if (targetArray[rowIndex]) {
            rowData.forEach((cellData, cellIndex) => {
                if (cellData && cellData !== '') {
                    // إذا كان العمود بعد العمود الجديد، نحتاج لتعديل الفهرس
                    let targetIndex = cellIndex;
                    if (cellIndex >= newColumnIndex) {
                        targetIndex = cellIndex + 1;
                    }

                    if (targetArray[rowIndex][targetIndex] !== undefined) {
                        targetArray[rowIndex][targetIndex] = cellData;
                    }
                }
            });
        }
    });
    debugLog('🔄 تم دمج البيانات المحفوظة مع المصفوفة الجديدة (مع العمود الجديد)');
}

// حذف عمود
function deleteColumn(columnIndex) {
    if (dutyData.headers.length > 3) {
        if (confirm('هل أنت متأكد من حذف هذا العمود؟')) {
            dutyData.headers.splice(columnIndex, 1);
            dutyData.rows.forEach(row => {
                row.splice(columnIndex, 1);
            });
            generateTable();
            saveDutyDataToServer(); // حفظ في قاعدة البيانات
        }
    }
}

// تحديث رأس العمود
function updateHeader(columnIndex, newValue) {
    if (dutyData.headers[columnIndex]) {
        dutyData.headers[columnIndex] = newValue;
        saveDutyDataToServer(); // حفظ في قاعدة البيانات
    }
}

// إضافة عمود جديد للجدول الرئيسي
function addColumn() {
    const newColumnName = prompt('أدخل اسم العمود الجديد:');
    if (newColumnName && newColumnName.trim()) {
        // إضافة العمود للرأس
        dutyData.headers.splice(-1, 0, newColumnName.trim()); // إدراج قبل عمود الملاحظات

        // إضافة خلية فارغة لكل صف
        dutyData.rows.forEach(row => {
            row.splice(-1, 0, ''); // إدراج قبل خلية الملاحظات
        });

        generateTable();
        saveDutyDataToServer(); // حفظ في قاعدة البيانات
    }
}

// إضافة صف جديد لجدول الدوريات
function addPatrolRow() {
    console.log('🔄 إضافة صف جديد لجدول الدوريات...');

    // حفظ البيانات الحالية أولاً
    saveCurrentPatrolData();

    // إضافة الصف الجديد
    const newRow = Array(patrolData.headers.length).fill('');
    patrolData.rows.push(newRow);

    // إعادة إنشاء الجدول
    generatePatrolTable();

    // استعادة البيانات بعد إعادة الإنشاء
    setTimeout(() => {
        restorePatrolDataSimple();
        savePatrolDataToServer();
        console.log('✅ تم إضافة صف الدوريات مع الحفاظ على البيانات');
    }, 300);
}

// إضافة صف بعد صف محدد - جدول الدوريات
function addPatrolRowAfter(rowIndex) {
    console.log('🔄 إضافة صف جديد للدوريات...');

    try {
        // حفظ البيانات الحالية للدوريات
        saveCurrentPatrolData();

        const newRow = Array(patrolData.headers.length).fill('');
        patrolData.rows.splice(rowIndex + 1, 0, newRow);
        generatePatrolTable();

        // استعادة البيانات
        setTimeout(() => {
            restorePatrolDataSimple();
            savePatrolDataToServer();
            console.log('✅ تم إضافة صف الدورية مع الحفاظ على البيانات');
        }, 300);
    } catch (error) {
        console.error('❌ خطأ في إضافة صف الدورية:', error);
    }
}

// حذف صف - جدول الدوريات
function deletePatrolRow(rowIndex) {
    // فحص وإصلاح البيانات أولاً
    validateAndFixDataStructure();

    console.log('🔍 فحص إمكانية حذف صف الدورية:', {
        'عدد الصفوف الحالي': patrolData.rows.length,
        'فهرس الصف المراد حذفه': rowIndex,
        'نوع البيانات': typeof patrolData.rows,
        'البيانات صحيحة': Array.isArray(patrolData.rows)
    });

    if (patrolData.rows.length > 1) {
        if (confirm('هل أنت متأكد من حذف هذا الصف؟')) {
            console.log(`🗑️ حذف الصف ${rowIndex + 1} من جدول الدوريات`);

            // حذف الصف مباشرة
            patrolData.rows.splice(rowIndex, 1);

            // إعادة إنشاء الجدول
            generatePatrolTable();

            // حفظ البيانات المحدثة فوراً وتحديث القوائم
            setTimeout(async () => {
                // حفظ فوري ودائم في جميع الأنظمة
                const saved = await saveDeletedDataPermanently();

                if (saved) {
                    // تحديث قوائم المواقع والأفراد لإزالة القيود
                    refreshAllLocationSelects();
                    refreshAllPersonnelSelects();

                    // إشعار المستخدم بنجاح الحذف
                    if (typeof showNotification === 'function') {
                        showNotification('✅ تم حذف صف الدورية وحفظ التغييرات نهائياً', 'success');
                    }

                    console.log('✅ تم حذف صف الدورية وحفظ البيانات المحدثة نهائياً');
                } else {
                    console.error('❌ فشل في حفظ بيانات الدورية المحذوفة');
                    if (typeof showNotification === 'function') {
                        showNotification('❌ فشل في حفظ تغييرات الدورية', 'error');
                    }
                }
            }, 100);
        }
    } else {
        console.error('❌ منع حذف صف الدورية:', {
            'عدد الصفوف': patrolData.rows.length,
            'السبب': 'عدد الصفوف أقل من أو يساوي 1',
            'محتوى البيانات': patrolData.rows
        });
        alert(`لا يمكن حذف الصف الوحيد المتبقي من جدول الدوريات\nعدد الصفوف الحالي: ${patrolData.rows.length}`);
    }
}

// إضافة عمود بعد عمود محدد - جدول الدوريات
function addPatrolColumnAfter(columnIndex) {
    console.log('🔄 إضافة عمود جديد للدوريات...');

    // حفظ البيانات الحالية
    saveCurrentTableData();

    patrolData.headers.splice(columnIndex + 1, 0, 'عمود جديد');
    patrolData.rows.forEach(row => {
        row.splice(columnIndex + 1, 0, '');
    });
    generatePatrolTable();

    // استعادة البيانات
    setTimeout(() => {
        restoreTableData();
        savePatrolDataToServer();
        console.log('✅ تم إضافة عمود الدورية مع الحفاظ على البيانات');
    }, 100);
}

// حذف عمود - جدول الدوريات
function deletePatrolColumn(columnIndex) {
    if (patrolData.headers.length > 3) {
        if (confirm('هل أنت متأكد من حذف هذا العمود؟')) {
            patrolData.headers.splice(columnIndex, 1);
            patrolData.rows.forEach(row => {
                row.splice(columnIndex, 1);
            });
            generatePatrolTable();
            savePatrolDataToServer(); // حفظ في قاعدة البيانات
        }
    }
}

// تحديث رأس العمود - جدول الدوريات
function updatePatrolHeader(columnIndex, newValue) {
    if (patrolData.headers[columnIndex]) {
        patrolData.headers[columnIndex] = newValue;
        savePatrolDataToServer(); // حفظ في قاعدة البيانات
    }
}

// إضافة عمود جديد لجدول الدوريات
function addPatrolColumn() {
    const newColumnName = prompt('أدخل اسم العمود الجديد:');
    if (newColumnName && newColumnName.trim()) {
        patrolData.headers.splice(-1, 0, newColumnName.trim());
        patrolData.rows.forEach(row => {
            row.splice(-1, 0, '');
        });
        generatePatrolTable();
    }
}

// إضافة صف جديد لجدول المناوبين
function addShiftsRow() {
    console.log('🔄 إضافة صف جديد لجدول المناوبين...');

    // حفظ البيانات الحالية أولاً
    saveCurrentShiftsData();

    // إضافة الصف الجديد
    const newRow = Array(shiftsData.headers.length).fill('');
    shiftsData.rows.push(newRow);

    // إعادة إنشاء الجدول
    generateShiftsTable();

    // استعادة البيانات بعد إعادة الإنشاء
    setTimeout(() => {
        restoreShiftsDataSimple();
        saveShiftsDataToServer();
        console.log('✅ تم إضافة صف المناوبين مع الحفاظ على البيانات');
    }, 300);
}

// إضافة صف بعد صف محدد - جدول المناوبين
function addShiftsRowAfter(rowIndex) {
    console.log('🔄 إضافة صف جديد للمناوبين...');

    try {
        // حفظ البيانات الحالية للمناوبين
        saveCurrentShiftsData();

        const newRow = Array(shiftsData.headers.length).fill('');
        shiftsData.rows.splice(rowIndex + 1, 0, newRow);
        generateShiftsTable();

        // استعادة البيانات
        setTimeout(() => {
            restoreShiftsDataSimple();
            saveShiftsDataToServer();
            console.log('✅ تم إضافة صف المناوبة مع الحفاظ على البيانات');
        }, 300);
    } catch (error) {
        console.error('❌ خطأ في إضافة صف المناوبة:', error);
    }
}

// حذف صف - جدول المناوبين
function deleteShiftsRow(rowIndex) {
    // فحص وإصلاح البيانات أولاً
    validateAndFixDataStructure();

    console.log('🔍 فحص إمكانية حذف صف المناوبة:', {
        'عدد الصفوف الحالي': shiftsData.rows.length,
        'فهرس الصف المراد حذفه': rowIndex,
        'نوع البيانات': typeof shiftsData.rows,
        'البيانات صحيحة': Array.isArray(shiftsData.rows)
    });

    if (shiftsData.rows.length > 1) {
        if (confirm('هل أنت متأكد من حذف هذا الصف؟')) {
            console.log(`🗑️ حذف الصف ${rowIndex + 1} من جدول المناوبين`);

            // حذف الصف مباشرة
            shiftsData.rows.splice(rowIndex, 1);

            // إعادة إنشاء الجدول
            generateShiftsTable();

            // حفظ البيانات المحدثة فوراً وتحديث القوائم
            setTimeout(async () => {
                // حفظ فوري ودائم في جميع الأنظمة
                const saved = await saveDeletedDataPermanently();

                if (saved) {
                    // تحديث قوائم المواقع والأفراد لإزالة القيود
                    refreshAllLocationSelects();
                    refreshAllPersonnelSelects();

                    // إشعار المستخدم بنجاح الحذف
                    if (typeof showNotification === 'function') {
                        showNotification('✅ تم حذف صف المناوبة وحفظ التغييرات نهائياً', 'success');
                    }

                    console.log('✅ تم حذف صف المناوبة وحفظ البيانات المحدثة نهائياً');
                } else {
                    console.error('❌ فشل في حفظ بيانات المناوبة المحذوفة');
                    if (typeof showNotification === 'function') {
                        showNotification('❌ فشل في حفظ تغييرات المناوبة', 'error');
                    }
                }
            }, 100);
        }
    } else {
        console.error('❌ منع حذف صف المناوبة:', {
            'عدد الصفوف': shiftsData.rows.length,
            'السبب': 'عدد الصفوف أقل من أو يساوي 1',
            'محتوى البيانات': shiftsData.rows
        });
        alert(`لا يمكن حذف الصف الوحيد المتبقي من جدول المناوبين\nعدد الصفوف الحالي: ${shiftsData.rows.length}`);
    }
}

// إضافة عمود بعد عمود محدد - جدول المناوبين
function addShiftsColumnAfter(columnIndex) {
    console.log('🔄 إضافة عمود جديد للمناوبين...');

    // حفظ البيانات الحالية
    saveCurrentTableData();

    shiftsData.headers.splice(columnIndex + 1, 0, 'عمود جديد');
    shiftsData.rows.forEach(row => {
        row.splice(columnIndex + 1, 0, '');
    });
    generateShiftsTable();

    // استعادة البيانات
    setTimeout(() => {
        restoreTableData();
        saveShiftsDataToServer();
        console.log('✅ تم إضافة عمود المناوبة مع الحفاظ على البيانات');
    }, 100);
}

// حذف عمود - جدول المناوبين
function deleteShiftsColumn(columnIndex) {
    if (shiftsData.headers.length > 3) {
        if (confirm('هل أنت متأكد من حذف هذا العمود؟')) {
            shiftsData.headers.splice(columnIndex, 1);
            shiftsData.rows.forEach(row => {
                row.splice(columnIndex, 1);
            });
            generateShiftsTable();
            saveShiftsDataToServer(); // حفظ في قاعدة البيانات
        }
    }
}

// تحديث رأس العمود - جدول المناوبين
function updateShiftsHeader(columnIndex, newValue) {
    if (shiftsData.headers[columnIndex]) {
        shiftsData.headers[columnIndex] = newValue;
        saveShiftsDataToServer(); // حفظ في قاعدة البيانات
    }
}

// دالة حفظ الكشف
async function saveReceipt() {
    try {
        console.log('🔄 بدء عملية حفظ الكشف...');

        const receiptData = {
            day_name: document.getElementById('dayName').value,
            hijri_date: document.getElementById('hijriDate').value,
            gregorian_date: document.getElementById('gregorianDate').value,
            receipt_number: document.getElementById('receiptNumber').value,
            duty_data: dutyData,
            patrol_data: patrolData,
            shifts_data: shiftsData
        };

        console.log('📋 بيانات الكشف:', receiptData);

        // إعداد headers للطلب
        let headers = {
            'Content-Type': 'application/json'
        };

        // محاولة الحصول على CSRF token (اختياري لأنه معطل)
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const tokenValue = csrfToken.getAttribute('content');
            console.log('🔐 CSRF token:', tokenValue);
            if (tokenValue && tokenValue !== 'dummy_csrf_token') {
                headers['X-CSRFToken'] = tokenValue;
            }
        }

        console.log('📡 إرسال الطلب إلى الخادم...');
        const response = await fetch('/duties/api/save-receipt', {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(receiptData)
        });

        console.log('📡 استجابة الخادم:', response.status, response.statusText);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('📄 نتيجة الحفظ:', result);

        if (result.success) {
            alert('✅ تم حفظ الكشف بنجاح!');
            // حفظ محلي كنسخة احتياطية
            localStorage.setItem('lastDutyReceipt', JSON.stringify(receiptData));
        } else {
            alert('❌ خطأ في حفظ الكشف: ' + (result.message || 'خطأ غير معروف'));
        }
    } catch (error) {
        console.error('❌ خطأ في حفظ الكشف:', error);

        // حفظ محلي في حالة فشل الحفظ على الخادم
        try {
            localStorage.setItem('lastDutyReceipt', JSON.stringify(receiptData));
            alert('❌ فشل الحفظ على الخادم، ولكن تم حفظ نسخة محلية.\nخطأ: ' + error.message);
        } catch (localError) {
            alert('❌ خطأ في حفظ الكشف: ' + error.message);
        }
    }
}

// مسح جميع البيانات
async function clearAllData() {
    if (confirm('هل أنت متأكد من مسح جميع البيانات؟ لن يمكن التراجع عن هذا الإجراء.')) {
        try {
            console.log('🗑️ بدء مسح جميع البيانات...');

            // مسح البيانات من localStorage
            localStorage.removeItem('dutyFormData');
            localStorage.removeItem('lastDutyReceipt');

            // تسجيل وقت المسح لمنع إعادة التحميل
            localStorage.setItem('dutyDataCleared', Date.now().toString());

            // مسح المسودة من قاعدة البيانات
            try {
                const response = await fetch('/duties/api/clear-draft', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                    }
                });

                if (response.ok) {
                    console.log('✅ تم مسح المسودة من قاعدة البيانات');

                    // تأكيد إضافي: حفظ بيانات فارغة في قاعدة البيانات
                    const emptyData = {
                        dutyData: { headers: [...DEFAULT_HEADERS], rows: [] },
                        patrolData: { headers: [...DEFAULT_PATROL_HEADERS], rows: [] },
                        shiftsData: { headers: [...DEFAULT_SHIFTS_HEADERS], rows: [] },
                        dayName: '',
                        hijriDate: '',
                        gregorianDate: '',
                        receiptNumber: ''
                    };

                    await fetch('/duties/api/save-draft', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                        },
                        body: JSON.stringify(emptyData)
                    });

                    console.log('✅ تم حفظ بيانات فارغة في قاعدة البيانات');
                } else {
                    console.warn('⚠️ فشل في مسح المسودة من قاعدة البيانات');
                }
            } catch (error) {
                console.warn('⚠️ خطأ في مسح المسودة من قاعدة البيانات:', error);
            }

            // إعادة تعيين البيانات مع صف واحد فارغ
            dutyData.rows = [Array(dutyData.headers.length || 8).fill('')];
            patrolData.rows = [Array(patrolData.headers.length || 6).fill('')];
            shiftsData.rows = [Array(shiftsData.headers.length || 6).fill('')];

            // مسح الحقول
            document.getElementById('dayName').value = '';
            document.getElementById('hijriDate').value = '';
            document.getElementById('gregorianDate').value = '';
            document.getElementById('receiptNumber').value = '';

            // إعادة إنشاء الجداول فارغة
            generateTable();
            generatePatrolTable();
            generateShiftsTable();

            // لا تعيد تهيئة التواريخ ورقم الكشف - اتركها فارغة كما طلب المستخدم
            console.log('ℹ️ تم ترك الحقول فارغة كما طلب المستخدم');

            showNotification('✅ تم مسح جميع البيانات بنجاح', 'success');
            console.log('🗑️ تم مسح جميع البيانات بنجاح');

        } catch (error) {
            console.error('❌ خطأ في مسح البيانات:', error);
            showNotification('❌ حدث خطأ أثناء مسح البيانات', 'error');
        }
    }
}

// دالة تصدير إلى Excel (نفس نظام كشف الاستلامات)
async function exportToExcel() {
    try {
        console.log('🚀 بدء عملية التصدير...');

        // التحقق من وجود البيانات
        if (!dutyData || !dutyData.headers) {
            console.error('❌ بيانات كشف الواجبات غير موجودة');
            showNotification('لا توجد بيانات لتصديرها', 'warning');
            return;
        }

        // جمع معلومات الكشف
        const dayName = document.getElementById('dayName').value || '';
        const hijriDate = document.getElementById('hijriDate').value || '';
        const gregorianDate = document.getElementById('gregorianDate').value || '';
        const receiptNumber = document.getElementById('receiptNumber').value || '';

        console.log(`📋 معلومات الكشف: اليوم=${dayName}, هجري=${hijriDate}, ميلادي=${gregorianDate}, رقم=${receiptNumber}`);

        // تحضير بيانات الجداول مع تحويل الأرقام إلى أسماء وإضافة ترقيم صحيح
        const dutyTableData = {
            headers: dutyData.headers || [],
            rows: await convertRowsToNames(dutyData.rows || [], 'duty')
        };

        const patrolTableData = {
            headers: patrolData.headers || [],
            rows: await convertRowsToNames(patrolData.rows || [], 'patrol')
        };

        const shiftsTableData = {
            headers: shiftsData.headers || [],
            rows: await convertRowsToNames(shiftsData.rows || [], 'shifts')
        };

        console.log(`📊 ملخص البيانات: الواجبات (${dutyTableData.rows.length} صف), الدوريات (${patrolTableData.rows.length} صف), المناوبات (${shiftsTableData.rows.length} صف)`);

        // إعداد البيانات للإرسال
        const exportData = {
            duty_info: {
                day_name: dayName,
                hijri_date: hijriDate,
                gregorian_date: gregorianDate,
                receipt_number: receiptNumber
            },
            duty_table: dutyTableData,
            patrol_table: patrolTableData,
            shifts_table: shiftsTableData
        };

        console.log('📦 البيانات المعدة للإرسال:', exportData);

        // التحقق من وجود CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        const headers = {
            'Content-Type': 'application/json'
        };

        if (csrfToken) {
            headers['X-CSRFToken'] = csrfToken.getAttribute('content');
        }

        console.log('🌐 إرسال البيانات للخادم...');

        // إرسال البيانات للخادم لتصدير Excel
        fetch('/duties/export-excel', {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(exportData)
        })
        .then(response => {
            console.log('📡 استجابة الخادم:', response.status, response.statusText);
            if (response.ok) {
                return response.blob();
            }
            throw new Error(`فشل في تصدير الملف: ${response.status} ${response.statusText}`);
        })
        .then(blob => {
            console.log('📁 تم استلام الملف، حجم:', blob.size, 'بايت');

            // تحميل الملف
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;

            // اسم الملف
            const fileName = `كشف_الواجبات_${gregorianDate || new Date().toISOString().split('T')[0]}`;

            // تحديد امتداد الملف
            const fileExtension = blob.type.includes('excel') || blob.type.includes('spreadsheet') ? '.xlsx' : '.csv';
            a.download = fileName + fileExtension;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            console.log('✅ تم تحميل الملف بنجاح');

            // رسالة نجاح
            const successMessage = '📊 تم تصدير كشف الواجبات بنجاح';
            showNotification(successMessage, 'success', 4000);
        })
        .catch(error => {
            console.error('❌ خطأ في تصدير Excel:', error);
            showNotification('❌ حدث خطأ أثناء تصدير الملف: ' + error.message, 'error');
        });

    } catch (error) {
        console.error('❌ خطأ في إعداد بيانات التصدير:', error);
        showNotification('❌ حدث خطأ في إعداد البيانات للتصدير: ' + error.message, 'error');
    }
}

// دالة لتحويل الأرقام إلى أسماء في الصفوف
async function convertRowsToNames(rows, tableType) {
    const convertedRows = [];
    let validRowNumber = 1; // عداد للصفوف التي تحتوي على بيانات

    for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {
        const row = rows[rowIndex];
        const convertedRow = [...row]; // نسخة من الصف

        // التحقق من وجود بيانات في الصف (غير العمود الأول)
        const hasData = convertedRow.slice(1).some(cell => cell && cell.toString().trim() !== '');

        if (hasData) {
            // وضع رقم صحيح للصفوف التي تحتوي على بيانات فقط
            convertedRow[0] = validRowNumber;
            validRowNumber++;
        } else {
            // إذا لم يكن هناك بيانات، تخطي هذا الصف
            continue;
        }

        // تحديد أعمدة المواقع والأفراد حسب نوع الجدول
        let locationCol, personnelStartCol, personnelEndCol;

        switch (tableType) {
            case 'duty':
                locationCol = 1;
                personnelStartCol = 2;
                personnelEndCol = 7;
                break;
            case 'patrol':
                locationCol = 1;
                personnelStartCol = 2;
                personnelEndCol = 5;
                break;
            case 'shifts':
                locationCol = 1;
                personnelStartCol = 2;
                personnelEndCol = 4;
                break;
            default:
                convertedRows.push(convertedRow);
                continue;
        }

        // تحويل الموقع من رقم إلى اسم
        if (convertedRow[locationCol]) {
            const locationName = await getLocationNameById(convertedRow[locationCol]);
            if (locationName) {
                convertedRow[locationCol] = locationName;
            }
        }

        // تحويل الأفراد من أرقام إلى أسماء
        for (let col = personnelStartCol; col <= personnelEndCol; col++) {
            if (convertedRow[col]) {
                const personnelName = await getPersonnelNameById(convertedRow[col]);
                if (personnelName) {
                    convertedRow[col] = personnelName;
                }
            }
        }

        convertedRows.push(convertedRow);
    }

    return convertedRows;
}

// دالة للحصول على اسم الموقع بالرقم
async function getLocationNameById(locationId) {
    try {
        // البحث في المواقع المحملة مسبقاً
        if (window.locations) {
            const location = window.locations.find(loc => loc.id == locationId);
            return location ? location.name : locationId;
        }

        // إذا لم تكن المواقع محملة، جلبها من الخادم
        const response = await fetch('/duties/api/get-locations');
        if (response.ok) {
            const data = await response.json();
            if (data.success && data.locations) {
                const location = data.locations.find(loc => loc.id == locationId);
                return location ? location.name : locationId;
            }
        }

        return locationId; // إرجاع الرقم إذا فشل الحصول على الاسم
    } catch (error) {
        console.error('❌ خطأ في الحصول على اسم الموقع:', error);
        return locationId;
    }
}

// دالة للحصول على اسم الفرد بالرقم
async function getPersonnelNameById(personnelId) {
    try {
        // البحث في الأفراد المحملين مسبقاً
        for (const locationId in locationPersonnelMap) {
            const personnel = locationPersonnelMap[locationId];
            const person = personnel.find(p => p.id == personnelId);
            if (person) {
                return person.display_name || `${person.name} (${person.rank})`;
            }
        }

        // إذا لم يوجد، محاولة جلبه من الخادم
        const response = await fetch(`/duties/api/get-personnel/${personnelId}`);
        if (response.ok) {
            const data = await response.json();
            if (data.success && data.personnel) {
                const person = data.personnel;
                return person.display_name || `${person.name} (${person.rank})`;
            }
        }

        return personnelId; // إرجاع الرقم إذا فشل الحصول على الاسم
    } catch (error) {
        console.error('❌ خطأ في الحصول على اسم الفرد:', error);
        return personnelId;
    }
}

// تهيئة كشف الواجبات تلقائياً (مثل كشف الاستلامات)
async function initializeDutyReceipt() {
    try {
        console.log('🚀 بدء تهيئة كشف الواجبات...');

        // استخدم الطريقة التقليدية مباشرة (أكثر موثوقية)
        await initializeDutyReceiptManually();
    } catch (error) {
        console.error('❌ خطأ في تهيئة كشف الواجبات من API:', error);
        // في حالة فشل API، استخدم الطريقة التقليدية
        await initializeDutyReceiptManually();
    }
}

// تهيئة كشف الواجبات يدوياً (نسخة احتياطية)
async function initializeDutyReceiptManually() {
    console.log('📝 تهيئة كشف الواجبات يدوياً...');

    const now = new Date();

    // تحديث اليوم
    const dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    const dayName = dayNames[now.getDay()];
    document.getElementById('dayName').value = dayName;

    // تحديث التاريخ الميلادي
    const gregorianDate = now.toISOString().split('T')[0];
    document.getElementById('gregorianDate').value = gregorianDate;

    // تحديث التاريخ الهجري فوراً
    const hijriDate = await getCurrentHijriDate();
    document.getElementById('hijriDate').value = hijriDate;

    // رقم الكشف التلقائي
    const receiptNumber = generateDutyReceiptNumber();
    document.getElementById('receiptNumber').value = receiptNumber;

    console.log('✅ تم تهيئة كشف الواجبات يدوياً:', {
        dayName,
        gregorianDate,
        hijriDate,
        receiptNumber
    });
}

// الحصول على التاريخ الهجري الحالي من API
async function getCurrentHijriDate() {
    try {
        const response = await fetch('/reports/api/hijri-date');
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                console.log('✅ تم الحصول على التاريخ الهجري من API:', data.hijri_formatted);
                return data.hijri_formatted;
            }
        }
        throw new Error('فشل في الحصول على التاريخ من API');
    } catch (error) {
        console.error('خطأ في الحصول على التاريخ الهجري:', error);
        // استخدم الحساب البديل
        return fallbackHijriCalculation(new Date());
    }
}

// حساب بديل للتاريخ الهجري (محدث للتاريخ الصحيح)
function fallbackHijriCalculation(date) {
    // التاريخ المرجعي الصحيح: 17 يوليو 2025 = 22 محرم 1447هـ
    const baseGregorianDate = new Date('2025-07-17');
    const baseHijriDay = 22;
    const baseHijriMonth = 1; // محرم (الشهر الأول)
    const baseHijriYear = 1447;

    const daysDiff = Math.floor((date - baseGregorianDate) / (1000 * 60 * 60 * 24));

    const hijriMonths = [
        'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
        'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
    ];

    // أطوال الشهور الهجرية (تقريبية)
    const monthLengths = [30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 29];

    let hijriDay = baseHijriDay + daysDiff;
    let hijriMonth = baseHijriMonth;
    let hijriYear = baseHijriYear;

    // تعديل الأيام والشهور
    while (hijriDay > monthLengths[hijriMonth - 1]) {
        hijriDay -= monthLengths[hijriMonth - 1];
        hijriMonth++;
        if (hijriMonth > 12) {
            hijriMonth = 1;
            hijriYear++;
        }
    }

    while (hijriDay < 1) {
        hijriMonth--;
        if (hijriMonth < 1) {
            hijriMonth = 12;
            hijriYear--;
        }
        hijriDay += monthLengths[hijriMonth - 1];
    }

    return `${hijriDay.toString().padStart(2, '0')} ${hijriMonths[hijriMonth - 1]} ${hijriYear}هـ`;
}

// تحديث التواريخ تلقائياً باستخدام API
async function updateDatesAutomatically() {
    try {
        // الحصول على التاريخ والوقت من API
        const response = await fetch('/reports/api/hijri-date');
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                // تحديث اليوم
                const dayElement = document.getElementById('dayName');
                if (dayElement) {
                    dayElement.value = data.day_name;
                }

                // تحديث التاريخ الميلادي
                const gregorianElement = document.getElementById('gregorianDate');
                if (gregorianElement) {
                    gregorianElement.value = data.gregorian_formatted;
                }

                // تحديث التاريخ الهجري
                const hijriElement = document.getElementById('hijriDate');
                if (hijriElement) {
                    hijriElement.value = data.hijri_formatted;
                }

                console.log('✅ تم تحديث التواريخ تلقائياً:', {
                    dayName: data.day_name,
                    gregorianDate: data.gregorian_formatted,
                    hijriDate: data.hijri_formatted
                });
            }
        }
    } catch (error) {
        console.error('❌ خطأ في تحديث التواريخ تلقائياً:', error);
    }
}

// إعداد مستمعي أحداث التواريخ
function setupDateEventListeners() {
    // تحديث التاريخ الهجري عند تغيير التاريخ الميلادي
    const gregorianElement = document.getElementById('gregorianDate');
    if (gregorianElement) {
        gregorianElement.addEventListener('change', async function() {
            const date = new Date(this.value);
            const hijriDate = await convertToHijri(date);
            const hijriElement = document.getElementById('hijriDate');
            if (hijriElement) {
                hijriElement.value = hijriDate;
            }
            console.log('✅ تم تحديث التاريخ الهجري عند تغيير التاريخ الميلادي:', hijriDate);
        });
    }

    // حفظ تلقائي عند تغيير التاريخ الهجري
    const hijriElement = document.getElementById('hijriDate');
    if (hijriElement) {
        hijriElement.addEventListener('input', function() {
            console.log('📝 تم تغيير التاريخ الهجري:', this.value);
        });
    }
}

// تحويل التاريخ إلى هجري باستخدام API (دقيق ومعتمد على تقويم أم القرى)
async function convertToHijri(date) {
    try {
        // إذا كان التاريخ هو اليوم، استخدم API للحصول على التاريخ الحالي
        const today = new Date();
        const isToday = date.toDateString() === today.toDateString();

        if (isToday) {
            return await getCurrentHijriDate();
        }

        // للتواريخ الأخرى، استخدم الحساب البديل
        return fallbackHijriCalculation(date);
    } catch (error) {
        console.error('خطأ في تحويل التاريخ إلى هجري:', error);
        return fallbackHijriCalculation(date);
    }
}

// توليد رقم كشف تلقائي
function generateDutyReceiptNumber() {
    const now = new Date();
    return `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;
}

// إضافة عمود جديد لجدول المناوبين
function addShiftsColumn() {
    const newColumnName = prompt('أدخل اسم العمود الجديد:');
    if (newColumnName && newColumnName.trim()) {
        shiftsData.headers.splice(-1, 0, newColumnName.trim());
        shiftsData.rows.forEach(row => {
            row.splice(-1, 0, '');
        });
        generateShiftsTable();
    }
}

// دوال التفريغ
function resetHeaders() {
    if (confirm('هل أنت متأكد من تفريغ الكشف؟')) {
        // تسجيل وقت التفريغ لمنع إعادة التحميل
        localStorage.setItem('dutyDataCleared', Date.now().toString());

        dutyData.headers = [...DEFAULT_HEADERS];
        dutyData.rows = [];
        generateTable();

        // حفظ التغييرات
        saveDataToLocalStorage();
    }
}

function resetPatrolTable() {
    if (confirm('هل أنت متأكد من تفريغ جدول الدوريات؟')) {
        // تسجيل وقت التفريغ لمنع إعادة التحميل
        localStorage.setItem('dutyDataCleared', Date.now().toString());

        patrolData.headers = [...DEFAULT_PATROL_HEADERS];
        patrolData.rows = [];
        generatePatrolTable();

        // حفظ التغييرات
        saveDataToLocalStorage();
    }
}

function resetShiftsTable() {
    if (confirm('هل أنت متأكد من تفريغ جدول المناوبين؟')) {
        // تسجيل وقت التفريغ لمنع إعادة التحميل
        localStorage.setItem('dutyDataCleared', Date.now().toString());

        shiftsData.headers = [...DEFAULT_SHIFTS_HEADERS];
        shiftsData.rows = [];
        generateShiftsTable();

        // حفظ التغييرات
        saveDataToLocalStorage();
    }
}

// دوال الحفظ في قاعدة البيانات

// حفظ بيانات الجدول الرئيسي في قاعدة البيانات
function saveDutyDataToServer() {
    // حفظ اختيارات الأفراد أولاً
    const personnelSelections = savePersonnelSelections();

    const dataToSave = {
        dutyData: {
            headers: dutyData.headers,
            rows: dutyData.rows
        },
        patrolData: {
            headers: patrolData.headers,
            rows: patrolData.rows
        },
        shiftsData: {
            headers: shiftsData.headers,
            rows: shiftsData.rows
        },
        personnelSelections: personnelSelections,
        timestamp: new Date().toISOString()
    };

    console.log('💾 حفظ بيانات كشف الواجبات:', {
        dutyHeaders: dutyData.headers.length,
        dutyRows: dutyData.rows.length,
        patrolHeaders: patrolData.headers.length,
        patrolRows: patrolData.rows.length,
        shiftsHeaders: shiftsData.headers.length,
        shiftsRows: shiftsData.rows.length
    });

    // طباعة عينة من البيانات للتحقق
    console.log('📋 عينة من بيانات الجدول الرئيسي:', dutyData.rows.slice(0, 2));

    // التحقق من وجود اختيارات أفراد في البيانات
    let personnelCount = 0;
    dutyData.rows.forEach((row, rowIndex) => {
        for (let colIndex = 2; colIndex <= 7; colIndex++) {
            if (row[colIndex]) {
                personnelCount++;
                console.log(`👤 فرد محفوظ في [${rowIndex}, ${colIndex}]: ${row[colIndex]}`);
            }
        }
    });
    console.log(`👥 إجمالي الأفراد المحفوظين: ${personnelCount}`);

    // طباعة البيانات الكاملة التي سيتم حفظها
    console.log('📦 البيانات الكاملة للحفظ:', JSON.stringify(dataToSave, null, 2));

    fetch('/duties/api/save-duty-data', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        },
        body: JSON.stringify(dataToSave)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('✅ تم حفظ بيانات كشف الواجبات في قاعدة البيانات');

            // تحقق إضافي من نجاح الحفظ
            setTimeout(() => {
                console.log('🔍 التحقق من نجاح الحفظ...');
                verifyDataSaved();
            }, 1000);
        } else {
            console.error('❌ خطأ في حفظ بيانات كشف الواجبات:', data.error);

            // إعادة المحاولة في حالة الفشل
            setTimeout(() => {
                console.log('🔄 إعادة محاولة الحفظ...');
                saveDutyDataToServer();
            }, 2000);
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الاتصال بالخادم:', error);
    });
}

// حفظ بيانات جدول الدوريات في قاعدة البيانات
function savePatrolDataToServer() {
    console.log('💾 حفظ بيانات جدول الدوريات:', {
        headers: patrolData.headers.length,
        rows: patrolData.rows.length
    });

    // طباعة عينة من البيانات للتحقق
    console.log('📋 عينة من بيانات جدول الدوريات:', patrolData.rows.slice(0, 2));

    // التحقق من وجود اختيارات أفراد في البيانات
    let personnelCount = 0;
    patrolData.rows.forEach((row, rowIndex) => {
        for (let colIndex = 2; colIndex < patrolData.headers.length - 1; colIndex++) {
            if (row[colIndex]) {
                personnelCount++;
                console.log(`👤 فرد دورية محفوظ في [${rowIndex}, ${colIndex}]: ${row[colIndex]}`);
            }
        }
    });
    console.log(`👥 إجمالي أفراد الدوريات المحفوظين: ${personnelCount}`);

    const dataToSave = {
        headers: patrolData.headers,
        rows: patrolData.rows,
        timestamp: new Date().toISOString()
    };

    fetch('/duties/api/save-patrol-data', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        },
        body: JSON.stringify(dataToSave)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('✅ تم حفظ بيانات جدول الدوريات في قاعدة البيانات');
        } else {
            console.error('❌ خطأ في حفظ بيانات جدول الدوريات:', data.error);
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الاتصال بالخادم:', error);
    });
}

// حفظ بيانات جدول المناوبين في قاعدة البيانات
function saveShiftsDataToServer() {
    console.log('💾 حفظ بيانات جدول المناوبين:', {
        headers: shiftsData.headers.length,
        rows: shiftsData.rows.length
    });

    // طباعة عينة من البيانات للتحقق
    console.log('📋 عينة من بيانات جدول المناوبين:', shiftsData.rows.slice(0, 2));

    // التحقق من وجود اختيارات أفراد في البيانات
    let personnelCount = 0;
    shiftsData.rows.forEach((row, rowIndex) => {
        for (let colIndex = 2; colIndex <= 4; colIndex++) { // خانات الأفراد من 2 إلى 4
            if (row[colIndex]) {
                personnelCount++;
                console.log(`👤 فرد مناوبة محفوظ في [${rowIndex}, ${colIndex}]: ${row[colIndex]}`);
            }
        }
    });
    console.log(`👥 إجمالي أفراد المناوبين المحفوظين: ${personnelCount}`);

    const dataToSave = {
        headers: shiftsData.headers,
        rows: shiftsData.rows,
        timestamp: new Date().toISOString()
    };

    fetch('/duties/api/save-shifts-data', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        },
        body: JSON.stringify(dataToSave)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('✅ تم حفظ بيانات جدول المناوبين في قاعدة البيانات');
        } else {
            console.error('❌ خطأ في حفظ بيانات جدول المناوبين:', data.error);
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الاتصال بالخادم:', error);
    });
}

// دوال التحميل من قاعدة البيانات

// تحميل بيانات الجدول الرئيسي من قاعدة البيانات
async function loadDutyDataFromServer() {
    try {
        console.log('🔄 تحميل بيانات كشف الواجبات من قاعدة البيانات...');

        const response = await fetch('/duties/api/load-duty-data', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        });

        const result = await response.json();

        if (result.success && result.data) {
            console.log('✅ تم تحميل بيانات كشف الواجبات من قاعدة البيانات');
            console.log('📦 البيانات المستلمة:', result.data);

            // استخراج البيانات
            if (result.data.dutyData && result.data.dutyData.headers && result.data.dutyData.rows) {
                dutyData.headers = result.data.dutyData.headers;
                dutyData.rows = result.data.dutyData.rows;
                console.log('📋 تم تحميل بيانات الجدول الرئيسي:', dutyData.headers.length, 'أعمدة،', dutyData.rows.length, 'صفوف');
                console.log('📋 عينة من البيانات:', dutyData.rows.slice(0, 2));
            }

            if (result.data.patrolData && result.data.patrolData.headers && result.data.patrolData.rows) {
                patrolData.headers = result.data.patrolData.headers;
                patrolData.rows = result.data.patrolData.rows;
                console.log('🚶 تم تحميل بيانات جدول الدوريات:', patrolData.headers.length, 'أعمدة،', patrolData.rows.length, 'صفوف');
            }

            if (result.data.shiftsData && result.data.shiftsData.headers && result.data.shiftsData.rows) {
                shiftsData.headers = result.data.shiftsData.headers;
                shiftsData.rows = result.data.shiftsData.rows;
                console.log('👥 تم تحميل بيانات جدول المناوبين:', shiftsData.headers.length, 'أعمدة،', shiftsData.rows.length, 'صفوف');
            }

            // تحميل اختيارات الأفراد المحفوظة
            if (result.data.personnelSelections) {
                console.log('👥 تم العثور على اختيارات أفراد محفوظة:', result.data.personnelSelections);
                localStorage.setItem('personnelSelections', JSON.stringify(result.data.personnelSelections));
            }

            // إعادة إنشاء الجداول
            generateTable();
            generatePatrolTable();
            generateShiftsTable();

            // انتظار قصير للتأكد من إنشاء الجداول ثم إعادة تطبيق الأفراد
            setTimeout(async () => {
                console.log('🔄 بدء إعادة تطبيق اختيارات الأفراد...');
                await reapplyPersonnelSelections();

                // تحقق إضافي بعد إعادة التطبيق
                setTimeout(() => {
                    validateSavedPersonnelData();
                    validateAllTablesData();
                }, 1000);

                // إعادة تطبيق إضافية بعد 3 ثوان للتأكد
                setTimeout(async () => {
                    console.log('🔄 إعادة تطبيق إضافية للتأكد من البيانات...');
                    await reapplyPersonnelSelections();
                }, 3000);
            }, 800);

            return true;
        } else {
            console.log('ℹ️ لا توجد بيانات محفوظة في قاعدة البيانات');

            // إنشاء صف افتراضي إذا لم توجد بيانات
            let needsSave = false;
            if (dutyData.rows.length === 0) {
                console.log('📋 إنشاء صف افتراضي للجدول الرئيسي');
                dutyData.rows.push(Array(dutyData.headers.length).fill(''));
                needsSave = true;
            }
            if (patrolData.rows.length === 0) {
                console.log('🚶 إنشاء صف افتراضي لجدول الدوريات');
                patrolData.rows.push(Array(patrolData.headers.length).fill(''));
                needsSave = true;
            }
            if (shiftsData.rows.length === 0) {
                console.log('👥 إنشاء صف افتراضي لجدول المناوبين');
                shiftsData.rows.push(Array(shiftsData.headers.length).fill(''));
                needsSave = true;
            }

            // حفظ البيانات الافتراضية إذا تم إنشاؤها
            if (needsSave) {
                console.log('💾 حفظ البيانات الافتراضية في قاعدة البيانات');
                setTimeout(() => saveDutyDataToServer(), 500);
            }

            return false;
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل بيانات كشف الواجبات:', error);
        return false;
    }
}

// تحميل بيانات جدول الدوريات من قاعدة البيانات
async function loadPatrolDataFromServer() {
    try {
        console.log('🔄 تحميل بيانات جدول الدوريات من قاعدة البيانات...');

        const response = await fetch('/duties/api/load-patrol-data', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        });

        const result = await response.json();

        if (result.success && result.data && result.data.headers && result.data.rows) {
            console.log('✅ تم تحميل بيانات جدول الدوريات من قاعدة البيانات');

            patrolData.headers = result.data.headers;
            patrolData.rows = result.data.rows;
            console.log('🚶 جدول الدوريات:', patrolData.headers.length, 'أعمدة،', patrolData.rows.length, 'صفوف');

            // إعادة إنشاء جدول الدوريات
            generatePatrolTable();

            return true;
        } else {
            console.log('ℹ️ لا توجد بيانات محفوظة لجدول الدوريات');
            return false;
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل بيانات جدول الدوريات:', error);
        return false;
    }
}

// تحميل بيانات جدول المناوبين من قاعدة البيانات
async function loadShiftsDataFromServer() {
    try {
        console.log('🔄 تحميل بيانات جدول المناوبين من قاعدة البيانات...');

        const response = await fetch('/duties/api/load-shifts-data', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        });

        const result = await response.json();

        if (result.success && result.data && result.data.headers && result.data.rows) {
            console.log('✅ تم تحميل بيانات جدول المناوبين من قاعدة البيانات');

            shiftsData.headers = result.data.headers;
            shiftsData.rows = result.data.rows;
            console.log('👥 جدول المناوبين:', shiftsData.headers.length, 'أعمدة،', shiftsData.rows.length, 'صفوف');

            // إعادة إنشاء جدول المناوبين
            generateShiftsTable();

            return true;
        } else {
            console.log('ℹ️ لا توجد بيانات محفوظة لجدول المناوبين');
            return false;
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل بيانات جدول المناوبين:', error);
        return false;
    }
}

// دالة خاصة لحفظ اختيارات الأفراد
function savePersonnelSelections() {
    console.log('💾 حفظ اختيارات الأفراد...');

    // جمع جميع اختيارات الأفراد من الجداول
    const personnelSelections = {
        dutyTable: [],
        patrolTable: [],
        shiftsTable: []
    };

    // جمع اختيارات الجدول الرئيسي
    for (let rowIndex = 0; rowIndex < dutyData.rows.length; rowIndex++) {
        const row = dutyData.rows[rowIndex];
        const personnelInRow = [];

        // خانات الأفراد من العمود 2 إلى 7
        for (let colIndex = 2; colIndex <= 7; colIndex++) {
            if (row[colIndex]) {
                personnelInRow.push({
                    column: colIndex,
                    personnelId: row[colIndex]
                });
            }
        }

        if (personnelInRow.length > 0) {
            personnelSelections.dutyTable.push({
                row: rowIndex,
                personnel: personnelInRow
            });
        }
    }

    console.log('👥 اختيارات الأفراد المحفوظة:', personnelSelections);

    // حفظ في localStorage كنسخة احتياطية
    localStorage.setItem('personnelSelections', JSON.stringify(personnelSelections));

    return personnelSelections;
}

// دالة إعادة تطبيق اختيارات الأفراد بعد تحميل البيانات
async function reapplyPersonnelSelections() {
    console.log('🔄 إعادة تطبيق اختيارات الأفراد...');

    // التحقق من وجود الجداول
    const dutyTable = document.getElementById('dutyTable');
    if (!dutyTable) {
        console.log('⚠️ جدول الواجبات غير موجود، سيتم المحاولة لاحقاً');
        setTimeout(reapplyPersonnelSelections, 1000);
        return;
    }

    // محاولة استعادة اختيارات الأفراد المحفوظة
    const savedSelections = localStorage.getItem('personnelSelections');
    if (savedSelections) {
        try {
            const selections = JSON.parse(savedSelections);
            console.log('📋 استعادة اختيارات الأفراد المحفوظة:', selections);
        } catch (error) {
            console.error('❌ خطأ في تحليل اختيارات الأفراد المحفوظة:', error);
        }
    }

    // إعادة تطبيق الأفراد في الجدول الرئيسي
    for (let rowIndex = 0; rowIndex < dutyData.rows.length; rowIndex++) {
        const row = dutyData.rows[rowIndex];
        const locationId = row[1]; // عمود الموقع

        if (locationId) {
            console.log(`🔄 إعادة تحميل أفراد الموقع ${locationId} للصف ${rowIndex}`);
            console.log(`📋 بيانات الصف ${rowIndex}:`, row);

            try {
                await loadPersonnelForLocation(locationId, rowIndex);
                // انتظار قصير بين كل صف
                await new Promise(resolve => setTimeout(resolve, 300));
            } catch (error) {
                console.error(`❌ خطأ في إعادة تحميل أفراد الصف ${rowIndex}:`, error);
            }
        }
    }

    // إعادة تطبيق الأفراد في جدول الدوريات
    for (let rowIndex = 0; rowIndex < patrolData.rows.length; rowIndex++) {
        const row = patrolData.rows[rowIndex];
        const locationId = row[1]; // عمود الموقع

        if (locationId) {
            console.log(`🔄 إعادة تحميل أفراد الموقع ${locationId} لدورية الصف ${rowIndex}`);
            console.log(`📋 بيانات صف الدورية ${rowIndex}:`, row);

            try {
                await loadPersonnelForPatrolLocation(locationId, rowIndex);
                // انتظار قصير للتأكد من تحميل الأفراد ثم تطبيق البيانات المحفوظة
                await new Promise(resolve => setTimeout(resolve, 200));

                // التأكد من تطبيق البيانات المحفوظة
                const personnel = locationPersonnelMap[locationId];
                if (personnel) {
                    console.log(`🔄 إعادة تطبيق البيانات المحفوظة لدورية الصف ${rowIndex}`);
                    updatePatrolPersonnelSelectsInRow(rowIndex, personnel);
                }

                // انتظار قصير بين كل صف
                await new Promise(resolve => setTimeout(resolve, 300));
            } catch (error) {
                console.error(`❌ خطأ في إعادة تحميل أفراد دورية الصف ${rowIndex}:`, error);
            }
        }
    }

    // إعادة تطبيق الأفراد في جدول المناوبين
    for (let rowIndex = 0; rowIndex < shiftsData.rows.length; rowIndex++) {
        const row = shiftsData.rows[rowIndex];
        const locationId = row[1]; // عمود الموقع

        if (locationId) {
            console.log(`🔄 إعادة تحميل أفراد الموقع ${locationId} لمناوبة الصف ${rowIndex}`);
            console.log(`📋 بيانات صف المناوبة ${rowIndex}:`, row);

            try {
                await loadPersonnelForShiftsLocation(locationId, rowIndex);
                // انتظار قصير للتأكد من تحميل الأفراد ثم تطبيق البيانات المحفوظة
                await new Promise(resolve => setTimeout(resolve, 200));

                // التأكد من تطبيق البيانات المحفوظة
                const personnel = locationPersonnelMap[locationId];
                if (personnel) {
                    console.log(`🔄 إعادة تطبيق البيانات المحفوظة لمناوبة الصف ${rowIndex}`);
                    updateShiftsPersonnelSelectsInRow(rowIndex, personnel);
                }

                // انتظار قصير بين كل صف
                await new Promise(resolve => setTimeout(resolve, 300));
            } catch (error) {
                console.error(`❌ خطأ في إعادة تحميل أفراد مناوبة الصف ${rowIndex}:`, error);
            }
        }
    }

    console.log('✅ تم إعادة تطبيق جميع اختيارات الأفراد');

    // التحقق من صحة البيانات المحفوظة
    setTimeout(() => {
        validateSavedPersonnelData();
        validateAllTablesData();
    }, 1000);
}

// دالة للتحقق من تطبيق البيانات في جميع الجداول
function validateAllTablesData() {
    console.log('🔍 التحقق النهائي من تطبيق البيانات في جميع الجداول...');

    // التحقق من الجدول الرئيسي
    let mainTablePersonnel = 0;
    dutyData.rows.forEach((row, rowIndex) => {
        for (let colIndex = 2; colIndex <= 7; colIndex++) {
            if (row[colIndex]) {
                mainTablePersonnel++;
                const select = document.querySelector(`#dutyTable tbody tr:nth-child(${rowIndex + 1}) td:nth-child(${colIndex + 1}) select`);
                if (select && select.value !== row[colIndex]) {
                    console.log(`⚠️ عدم تطابق في الجدول الرئيسي [${rowIndex}, ${colIndex}]: البيانات="${row[colIndex]}" القائمة="${select.value}"`);
                    select.value = row[colIndex];
                }
            }
        }
    });

    // التحقق من جدول الدوريات
    let patrolTablePersonnel = 0;
    patrolData.rows.forEach((row, rowIndex) => {
        for (let colIndex = 2; colIndex < patrolData.headers.length - 1; colIndex++) {
            if (row[colIndex]) {
                patrolTablePersonnel++;
                const select = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1}) td:nth-child(${colIndex + 1}) select`);
                if (select && select.value !== row[colIndex]) {
                    console.log(`⚠️ عدم تطابق في جدول الدوريات [${rowIndex}, ${colIndex}]: البيانات="${row[colIndex]}" القائمة="${select.value}"`);
                    select.value = row[colIndex];
                }
            }
        }
    });

    // التحقق من جدول المناوبين
    let shiftsTablePersonnel = 0;
    shiftsData.rows.forEach((row, rowIndex) => {
        for (let colIndex = 2; colIndex <= 4; colIndex++) {
            if (row[colIndex]) {
                shiftsTablePersonnel++;
                const select = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1}) td:nth-child(${colIndex + 1}) select`);
                if (select && select.value !== row[colIndex]) {
                    console.log(`⚠️ عدم تطابق في جدول المناوبين [${rowIndex}, ${colIndex}]: البيانات="${row[colIndex]}" القائمة="${select.value}"`);
                    select.value = row[colIndex];
                }
            }
        }
    });

    console.log(`📊 إحصائيات الأفراد المطبقة:`);
    console.log(`   - الجدول الرئيسي: ${mainTablePersonnel} فرد`);
    console.log(`   - جدول الدوريات: ${patrolTablePersonnel} فرد`);
    console.log(`   - جدول المناوبين: ${shiftsTablePersonnel} فرد`);
}

// دالة للتحقق من نجاح حفظ البيانات
async function verifyDataSaved() {
    try {
        console.log('🔍 التحقق من البيانات المحفوظة في قاعدة البيانات...');

        const response = await fetch('/duties/api/load-duty-data', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        });

        const result = await response.json();

        if (result.success && result.data && result.data.dutyData) {
            const savedData = result.data.dutyData.rows;
            console.log('📋 البيانات المحفوظة في قاعدة البيانات:', savedData);

            // مقارنة البيانات المحلية مع المحفوظة
            let personnelMatches = 0;
            let personnelMismatches = 0;

            for (let rowIndex = 0; rowIndex < dutyData.rows.length; rowIndex++) {
                const localRow = dutyData.rows[rowIndex];
                const savedRow = savedData[rowIndex];

                if (localRow && savedRow) {
                    for (let colIndex = 2; colIndex <= 7; colIndex++) {
                        if (localRow[colIndex] && savedRow[colIndex]) {
                            if (localRow[colIndex] === savedRow[colIndex]) {
                                personnelMatches++;
                            } else {
                                personnelMismatches++;
                                console.log(`⚠️ عدم تطابق في [${rowIndex}, ${colIndex}]: محلي="${localRow[colIndex]}" محفوظ="${savedRow[colIndex]}"`);
                            }
                        }
                    }
                }
            }

            console.log(`✅ تطابق الأفراد: ${personnelMatches}, عدم تطابق: ${personnelMismatches}`);

            if (personnelMismatches > 0) {
                console.log('🔄 إعادة حفظ البيانات بسبب عدم التطابق...');
                saveDutyDataToServer();
                savePatrolDataToServer();
                saveShiftsDataToServer();
            }
        } else {
            console.log('⚠️ لم يتم العثور على بيانات محفوظة');
        }
    } catch (error) {
        console.error('❌ خطأ في التحقق من البيانات المحفوظة:', error);
    }
}

// دالة للتحقق من صحة البيانات المحفوظة
function validateSavedPersonnelData() {
    console.log('🔍 التحقق من صحة البيانات المحفوظة...');

    for (let rowIndex = 0; rowIndex < dutyData.rows.length; rowIndex++) {
        const row = dutyData.rows[rowIndex];
        console.log(`📋 الصف ${rowIndex}:`, row);

        // التحقق من الأفراد المختارين في خانات الأفراد
        for (let colIndex = 2; colIndex <= 7; colIndex++) {
            if (row[colIndex]) {
                console.log(`👤 فرد محفوظ في [${rowIndex}, ${colIndex}]: ${row[colIndex]}`);

                // التحقق من وجود القائمة المنسدلة المقابلة
                const select = document.querySelector(`#dutyTable tbody tr:nth-child(${rowIndex + 1}) td:nth-child(${colIndex + 1}) select`);
                if (select) {
                    console.log(`✅ القائمة موجودة، القيمة الحالية: ${select.value}`);
                    if (select.value !== row[colIndex]) {
                        console.log(`⚠️ عدم تطابق! البيانات: ${row[colIndex]}, القائمة: ${select.value}`);
                    }
                } else {
                    console.log(`❌ القائمة المنسدلة غير موجودة للخانة [${rowIndex}, ${colIndex}]`);
                }
            }
        }
    }
}

// دالة تهيئة مبسطة للتواريخ
function initializeDutyReceiptSimple() {
    try {
        console.log('📅 تهيئة التواريخ...');

        const now = new Date();
        const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];

        // تحديث اليوم
        const dayElement = document.getElementById('dayName');
        if (dayElement) {
            dayElement.value = days[now.getDay()];
        }

        // تحديث التاريخ الميلادي
        const gregorianElement = document.getElementById('gregorianDate');
        if (gregorianElement) {
            gregorianElement.value = now.toISOString().split('T')[0];
        }

        // تحديث رقم الكشف
        const receiptElement = document.getElementById('receiptNumber');
        if (receiptElement) {
            receiptElement.value = 'D' + now.getFullYear() + (now.getMonth() + 1).toString().padStart(2, '0') + now.getDate().toString().padStart(2, '0');
        }

        console.log('✅ تم تهيئة التواريخ');

    } catch (error) {
        console.error('❌ خطأ في تهيئة التواريخ:', error);
    }
}

// دالة تحميل البيانات في الخلفية
async function loadDataInBackground() {
    try {
        console.log('🔄 تحميل البيانات في الخلفية...');

        // فحص ما إذا كانت البيانات قد تم مسحها مؤخراً
        const dataCleared = localStorage.getItem('dutyDataCleared');
        if (dataCleared) {
            const clearTime = parseInt(dataCleared);
            const now = Date.now();
            // إذا تم المسح خلال آخر 5 دقائق، لا تحمل البيانات
            if (now - clearTime < 300000) { // 5 دقائق
                console.log('⚠️ تم مسح البيانات مؤخراً - تجاهل التحميل');
                return;
            }
        }

        // محاولة تحميل البيانات من localStorage أولاً (أسرع)
        const localData = localStorage.getItem('dutyFormData');
        if (localData) {
            const data = JSON.parse(localData);
            console.log('✅ تم تحميل البيانات من localStorage');
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات:', error);
    }
}

// تم حذف دالة المواقع الافتراضية - سيتم استخدام البيانات الحقيقية فقط

// دالة بدء الحفظ التلقائي القوي والمحسن
function startAutoSave() {
    console.log('🔄 بدء نظام الحفظ التلقائي القوي والمحسن...');

    // حفظ في قاعدة البيانات كل 10 ثوان (أسرع)
    setInterval(() => {
        saveToDatabaseAndLocal();
    }, 10000);

    // حفظ فوري عند تغيير أي بيانات
    document.addEventListener('change', (event) => {
        console.log('📝 تغيير في البيانات - حفظ فوري');

        // حفظ فوري في localStorage
        saveDataToLocalStorage();

        // حفظ فوري في قاعدة البيانات للمواقع والأفراد
        if (event.target.classList.contains('location-select') ||
            event.target.classList.contains('personnel-select')) {
            console.log('🎯 تغيير في موقع أو فرد - حفظ فوري في قاعدة البيانات');
            setTimeout(() => saveToDatabaseAndLocal(), 500);
        } else {
            // للتغييرات الأخرى، حفظ بتأخير قصير
            clearTimeout(window.saveTimeout);
            window.saveTimeout = setTimeout(() => {
                saveToDatabaseAndLocal();
            }, 2000);
        }
    });

    // حفظ عند النقر على القوائم
    document.addEventListener('click', (event) => {
        if (event.target.tagName === 'SELECT' || event.target.tagName === 'OPTION') {
            setTimeout(() => {
                console.log('🖱️ نقر على قائمة - حفظ البيانات');
                saveDataToLocalStorage();
                saveToDatabaseAndLocal();
            }, 1000);
        }
    });

    // حفظ عند إغلاق الصفحة
    window.addEventListener('beforeunload', (event) => {
        console.log('🚪 إغلاق الصفحة - حفظ البيانات...');

        // حفظ فوري في localStorage
        saveDataToLocalStorage();

        // محاولة حفظ في قاعدة البيانات
        try {
            const data = collectAllData();
            const blob = new Blob([JSON.stringify(data)], { type: 'application/json' });
            navigator.sendBeacon('/duties/api/save-duty-data', blob);
            console.log('📡 تم إرسال البيانات عبر sendBeacon');
        } catch (error) {
            console.warn('⚠️ فشل في الحفظ عند إغلاق الصفحة:', error);
        }
    });

    // حفظ عند فقدان التركيز على النافذة
    window.addEventListener('blur', () => {
        console.log('👁️ فقدان التركيز على النافذة - حفظ البيانات...');
        saveDataToLocalStorage();
        saveToDatabaseAndLocal();
    });

    // حفظ عند إخفاء الصفحة (تبديل التبويب)
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            console.log('🙈 إخفاء الصفحة - حفظ البيانات...');
            saveDataToLocalStorage();
            saveToDatabaseAndLocal();
        } else {
            console.log('👀 إظهار الصفحة - تحميل أحدث البيانات...');
            setTimeout(() => loadDataInBackground(), 1000);
        }
    });

    // حفظ دوري كل 5 ثوان للتأكد
    setInterval(() => {
        saveDataToLocalStorage();
    }, 5000);

    console.log('✅ تم تفعيل نظام الحفظ التلقائي القوي');
}

// دالة بدء التحميل الدوري للبيانات
function startPeriodicDataLoad() {
    console.log('🔄 بدء نظام التحميل الدوري للبيانات...');

    // تحميل البيانات كل دقيقة للتأكد من التحديث
    setInterval(async () => {
        try {
            console.log('🔄 تحميل دوري للبيانات...');
            await loadDataInBackground();

            // تطبيق البيانات المحفوظة
            setTimeout(() => {
                applySelectedLocationsAndPersonnel();
                applyPatrolLocationsAndPersonnel();
                applyShiftsLocationsAndPersonnel();
            }, 1000);

        } catch (error) {
            console.warn('⚠️ خطأ في التحميل الدوري:', error);
        }
    }, 60000); // كل دقيقة

    // تحميل إضافي كل 30 ثانية لتطبيق المواقع والأفراد
    setInterval(() => {
        console.log('🔄 تطبيق دوري للمواقع والأفراد...');
        applySelectedLocationsAndPersonnel();
        applyPatrolLocationsAndPersonnel();
        applyShiftsLocationsAndPersonnel();
    }, 30000); // كل 30 ثانية

    console.log('✅ تم تفعيل نظام التحميل الدوري');
}

// دالة الحفظ المحسنة في قاعدة البيانات و localStorage
async function saveToDatabaseAndLocal() {
    try {
        console.log('💾 بدء عملية الحفظ المحسنة...');

        // تنظيف البيانات أولاً
        cleanupData();

        // حفظ في localStorage أولاً (سريع)
        saveDataToLocalStorage();

        // فحص الاتصال قبل محاولة الحفظ في قاعدة البيانات
        if (!navigator.onLine) {
            console.log('📴 لا يوجد اتصال - تم الحفظ محلياً فقط');
            showSaveNotification('تم الحفظ محلياً (لا يوجد اتصال)', 'warning');
            return;
        }

        // جمع جميع البيانات
        const allData = collectAllData();

        // حفظ في قاعدة البيانات
        await saveToDatabase(allData);

        console.log('✅ تم الحفظ بنجاح في قاعدة البيانات و localStorage');

        // إظهار إشعار نجاح (بدون إزعاج مفرط)
        if (Math.random() < 0.3) { // إظهار الإشعار في 30% من المرات فقط
            showSaveNotification('تم الحفظ بنجاح', 'success');
        }

    } catch (error) {
        console.error('❌ خطأ في الحفظ:', error);

        // في حالة الخطأ، تأكد من الحفظ في localStorage على الأقل
        saveDataToLocalStorage();
        showSaveNotification('خطأ في الحفظ - تم الحفظ محلياً', 'error');
    }
}

// دالة جمع جميع البيانات
function collectAllData() {
    const formData = {};

    // جمع بيانات النموذج
    const formElements = ['dayName', 'hijriDate', 'gregorianDate', 'receiptNumber'];
    formElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            formData[id] = element.value;
        }
    });

    return {
        timestamp: new Date().toISOString(),
        formData: formData,
        dutyData: dutyData,
        patrolData: patrolData,
        shiftsData: shiftsData
    };
}

// دالة الحفظ في قاعدة البيانات
async function saveToDatabase(data) {
    try {
        console.log('📡 حفظ البيانات في قاعدة البيانات...');

        // حفظ البيانات الرئيسية
        const response = await fetch('/duties/api/save-duty-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.error || 'فشل في حفظ البيانات');
        }

        console.log('✅ تم حفظ البيانات الرئيسية في قاعدة البيانات');

        // حفظ بيانات الدوريات
        if (patrolData.rows && patrolData.rows.length > 0) {
            await savePatrolToDatabase(patrolData);
        }

        // حفظ بيانات المناوبين
        if (shiftsData.rows && shiftsData.rows.length > 0) {
            await saveShiftsToDatabase(shiftsData);
        }

        // تحديث وقت آخر مزامنة
        localStorage.setItem('lastDatabaseSync', new Date().toISOString());

        return true;

    } catch (error) {
        console.error('❌ خطأ في حفظ البيانات في قاعدة البيانات:', error);
        throw error;
    }
}

// دالة حفظ بيانات الدوريات
async function savePatrolToDatabase(data) {
    try {
        const response = await fetch('/duties/api/save-patrol-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                console.log('✅ تم حفظ بيانات الدوريات');
            }
        }
    } catch (error) {
        console.warn('⚠️ خطأ في حفظ بيانات الدوريات:', error);
    }
}

// دالة حفظ بيانات المناوبين
async function saveShiftsToDatabase(data) {
    try {
        const response = await fetch('/duties/api/save-shifts-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                console.log('✅ تم حفظ بيانات المناوبين');
            }
        }
    } catch (error) {
        console.warn('⚠️ خطأ في حفظ بيانات المناوبين:', error);
    }
}

// دالة إظهار إشعار الحفظ
function showSaveNotification(message, type = 'info') {
    // إزالة الإشعار السابق إن وجد
    const existingNotification = document.getElementById('saveNotification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // إنشاء إشعار جديد
    const notification = document.createElement('div');
    notification.id = 'saveNotification';
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 10px 20px;
        border-radius: 5px;
        color: white;
        font-weight: bold;
        z-index: 10000;
        transition: opacity 0.3s;
        ${type === 'success' ? 'background-color: #28a745;' : 'background-color: #dc3545;'}
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // إخفاء الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// دالة إضافة event listeners لحقول النموذج
function addFormFieldListeners() {
    console.log('🔗 إضافة event listeners لحقول النموذج...');

    const formFields = ['dayName', 'hijriDate', 'gregorianDate', 'receiptNumber'];

    formFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            // إضافة listener للتغيير
            field.addEventListener('change', () => {
                console.log(`📝 تغيير في حقل ${fieldId}: ${field.value}`);

                // حفظ فوري في localStorage
                saveDataToLocalStorage();

                // حفظ في قاعدة البيانات بعد تأخير قصير
                clearTimeout(window.formFieldTimeout);
                window.formFieldTimeout = setTimeout(() => {
                    saveToDatabaseAndLocal();
                }, 2000);
            });

            // إضافة listener لفقدان التركيز
            field.addEventListener('blur', () => {
                console.log(`💾 حفظ عند فقدان التركيز من حقل ${fieldId}`);
                saveDataToLocalStorage();

                clearTimeout(window.formFieldBlurTimeout);
                window.formFieldBlurTimeout = setTimeout(() => {
                    saveToDatabaseAndLocal();
                }, 1000);
            });

            console.log(`✅ تم إضافة listeners للحقل ${fieldId}`);
        } else {
            console.warn(`⚠️ لم يتم العثور على الحقل ${fieldId}`);
        }
    });

    console.log('✅ تم إضافة جميع event listeners لحقول النموذج');
}

// دالة تطبيق المواقع والأفراد المحفوظة للجدول الرئيسي
function applySelectedLocationsAndPersonnel() {
    try {
        console.log('🔄 تطبيق المواقع والأفراد المحفوظة للجدول الرئيسي...');

        dutyData.rows.forEach((row, rowIndex) => {
            // تطبيق الموقع المحفوظ
            const locationValue = row[1]; // عمود الموقع
            if (locationValue) {
                const locationSelect = document.querySelector(`#dutyTable tbody tr:nth-child(${rowIndex + 1}) td:nth-child(2) select`);
                if (locationSelect) {
                    // البحث الذكي عن الموقع وتطبيقه
                    let locationId = null;

                    // محاولة 1: إذا كان القيمة رقم، استخدمها مباشرة
                    if (!isNaN(locationValue) && locationValue !== '' && locationValue > 0) {
                        locationId = locationValue;
                        locationSelect.value = locationValue;
                        console.log(`📍 تطبيق موقع برقم: ${locationValue}`);
                    } else if (locationValue && locationValue !== '') {
                        // محاولة 2: البحث بالاسم في النص
                        const options = locationSelect.querySelectorAll('option');
                        for (let option of options) {
                            const optionText = option.textContent.trim();
                            const optionValue = option.value;

                            // البحث بالاسم الكامل أو الجزئي
                            if (optionText.includes(locationValue) ||
                                locationValue.includes(optionText) ||
                                optionText.toLowerCase().includes(locationValue.toLowerCase()) ||
                                locationValue.toLowerCase().includes(optionText.toLowerCase())) {
                                locationId = optionValue;
                                locationSelect.value = optionValue;
                                console.log(`📍 تطبيق موقع بالاسم: "${locationValue}" -> "${optionText}" (ID: ${optionValue})`);
                                break;
                            }
                        }
                    }

                    console.log(`📍 تطبيق موقع محفوظ للصف ${rowIndex + 1}: ${locationValue}`);

                    // البحث عن رقم الموقع لتحميل الأفراد
                    let numericLocationId = null;
                    if (locationValue && locationsDatabase) {
                        const location = locationsDatabase.find(loc => loc.name === locationValue);
                        if (location) {
                            numericLocationId = location.id;
                            console.log(`📍 تم العثور على الموقع: "${locationValue}" -> ID: ${numericLocationId}`);
                        }
                    }

                    // تحميل أفراد الموقع فقط إذا كان لدينا ID صحيح
                    if (numericLocationId && !isNaN(numericLocationId)) {
                        console.log(`🔄 تحميل أفراد للموقع ID: ${numericLocationId}`);
                        loadPersonnelForLocation(numericLocationId, rowIndex).then(() => {
                            // تطبيق الأفراد المحفوظين بعد تحميل قائمة الأفراد
                            setTimeout(() => {
                                for (let colIndex = 2; colIndex <= 7; colIndex++) {
                                    const personnelValue = row[colIndex];
                                    if (personnelValue) {
                                        const personnelSelect = document.querySelector(`#dutyTable tbody tr:nth-child(${rowIndex + 1}) td:nth-child(${colIndex + 1}) select`);
                                        if (personnelSelect) {
                                            personnelSelect.value = personnelValue;
                                            console.log(`👤 تطبيق فرد محفوظ للصف ${rowIndex + 1}, العمود ${colIndex + 1}: ${personnelValue}`);
                                        }
                                    }
                                }
                            }, 300);
                        }).catch(error => {
                            console.error(`❌ خطأ في تحميل أفراد الموقع ${locationId}:`, error);
                        });
                    } else {
                        console.warn(`⚠️ معرف الموقع غير صالح: ${locationId} (القيمة الأصلية: ${locationValue})`);

                        // محاولة تطبيق الأفراد بدون تحميل أفراد الموقع
                        setTimeout(() => {
                            for (let colIndex = 2; colIndex <= 7; colIndex++) {
                                const personnelValue = row[colIndex];
                                if (personnelValue) {
                                    const personnelSelect = document.querySelector(`#dutyTable tbody tr:nth-child(${rowIndex + 1}) td:nth-child(${colIndex + 1}) select`);
                                    if (personnelSelect) {
                                        // إضافة الخيار إذا لم يكن موجوداً
                                        let optionExists = false;
                                        for (let option of personnelSelect.options) {
                                            if (option.value === personnelValue) {
                                                optionExists = true;
                                                break;
                                            }
                                        }

                                        if (!optionExists) {
                                            const newOption = document.createElement('option');
                                            newOption.value = personnelValue;
                                            newOption.textContent = personnelValue;
                                            personnelSelect.appendChild(newOption);
                                        }

                                        personnelSelect.value = personnelValue;
                                        console.log(`👤 تطبيق فرد محفوظ بدون موقع للصف ${rowIndex + 1}, العمود ${colIndex + 1}: ${personnelValue}`);
                                    }
                                }
                            }
                        }, 300);
                    }
                }
            }
        });

        console.log('✅ تم تطبيق المواقع والأفراد المحفوظة للجدول الرئيسي');

    } catch (error) {
        console.error('❌ خطأ في تطبيق المواقع والأفراد:', error);
    }
}

// دالة تطبيق المواقع والأفراد المحفوظة لجدول الدوريات
function applyPatrolLocationsAndPersonnel() {
    try {
        console.log('🔄 تطبيق المواقع والأفراد المحفوظة لجدول الدوريات...');

        patrolData.rows.forEach((row, rowIndex) => {
            const locationValue = row[1]; // عمود الموقع
            if (locationValue) {
                const locationSelect = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1}) td:nth-child(2) select`);
                if (locationSelect) {
                    locationSelect.value = locationValue;
                    console.log(`📍 تطبيق موقع دورية محفوظ للصف ${rowIndex + 1}: ${locationValue}`);

                    // البحث عن رقم الموقع لتحميل الأفراد
                    let numericLocationId = null;
                    if (locationValue && locationsDatabase) {
                        const location = locationsDatabase.find(loc => loc.name === locationValue);
                        if (location) {
                            numericLocationId = location.id;
                            console.log(`📍 تم العثور على موقع الدورية: "${locationValue}" -> ID: ${numericLocationId}`);
                        }
                    }

                    // تحميل أفراد الموقع وتطبيق الأفراد المحفوظين
                    if (numericLocationId) {
                        loadPersonnelForPatrolLocation(numericLocationId, rowIndex).then(() => {
                        setTimeout(() => {
                            for (let colIndex = 2; colIndex < row.length - 1; colIndex++) {
                                const personnelValue = row[colIndex];
                                if (personnelValue) {
                                    const personnelSelect = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1}) td:nth-child(${colIndex + 1}) select`);
                                    if (personnelSelect) {
                                        personnelSelect.value = personnelValue;
                                        console.log(`👤 تطبيق فرد دورية محفوظ للصف ${rowIndex + 1}, العمود ${colIndex + 1}: ${personnelValue}`);
                                    }
                                }
                            }
                        }, 300);
                    });
                    }
                }
            }
        });

        console.log('✅ تم تطبيق المواقع والأفراد المحفوظة لجدول الدوريات');

    } catch (error) {
        console.error('❌ خطأ في تطبيق مواقع وأفراد الدوريات:', error);
    }
}

// دالة تطبيق المواقع والأفراد المحفوظة لجدول المناوبين
function applyShiftsLocationsAndPersonnel() {
    try {
        console.log('🔄 تطبيق المواقع والأفراد المحفوظة لجدول المناوبين...');

        shiftsData.rows.forEach((row, rowIndex) => {
            const locationValue = row[1]; // عمود الموقع
            if (locationValue) {
                const locationSelect = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1}) td:nth-child(2) select`);
                if (locationSelect) {
                    locationSelect.value = locationValue;
                    console.log(`📍 تطبيق موقع مناوبة محفوظ للصف ${rowIndex + 1}: ${locationValue}`);

                    // البحث عن رقم الموقع لتحميل الأفراد
                    let numericLocationId = null;
                    if (locationValue && locationsDatabase) {
                        const location = locationsDatabase.find(loc => loc.name === locationValue);
                        if (location) {
                            numericLocationId = location.id;
                            console.log(`📍 تم العثور على موقع المناوبة: "${locationValue}" -> ID: ${numericLocationId}`);
                        }
                    }

                    // تحميل أفراد الموقع وتطبيق الأفراد المحفوظين
                    if (numericLocationId) {
                        loadPersonnelForShiftsLocation(numericLocationId, rowIndex).then(() => {
                        setTimeout(() => {
                            for (let colIndex = 2; colIndex < row.length - 1; colIndex++) {
                                const personnelValue = row[colIndex];
                                if (personnelValue) {
                                    const personnelSelect = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1}) td:nth-child(${colIndex + 1}) select`);
                                    if (personnelSelect) {
                                        personnelSelect.value = personnelValue;
                                        console.log(`👤 تطبيق فرد مناوبة محفوظ للصف ${rowIndex + 1}, العمود ${colIndex + 1}: ${personnelValue}`);
                                    }
                                }
                            }
                        }, 300);
                    });
                    }
                }
            }
        });

        console.log('✅ تم تطبيق المواقع والأفراد المحفوظة لجدول المناوبين');

    } catch (error) {
        console.error('❌ خطأ في تطبيق مواقع وأفراد المناوبين:', error);
    }
}

// دالة تنظيف البيانات وإزالة التكرارات - محسنة
function cleanupData() {
    try {
        console.log('🧹 تنظيف البيانات...');

        // تنظيف بيانات الجدول الرئيسي
        if (dutyData.rows) {
            dutyData.rows = dutyData.rows.filter(row => {
                // إزالة الصفوف الفارغة تماماً
                return row && row.some(cell => {
                    // فحص آمن للخلايا
                    if (cell === null || cell === undefined) return false;
                    if (typeof cell === 'string') return cell.trim() !== '';
                    if (typeof cell === 'number') return cell !== 0;
                    return Boolean(cell);
                });
            });
        }

        // تنظيف بيانات الدوريات
        if (patrolData.rows) {
            patrolData.rows = patrolData.rows.filter(row => {
                return row && row.some(cell => {
                    if (cell === null || cell === undefined) return false;
                    if (typeof cell === 'string') return cell.trim() !== '';
                    if (typeof cell === 'number') return cell !== 0;
                    return Boolean(cell);
                });
            });
        }

        // تنظيف بيانات المناوبين
        if (shiftsData.rows) {
            shiftsData.rows = shiftsData.rows.filter(row => {
                return row && row.some(cell => {
                    if (cell === null || cell === undefined) return false;
                    if (typeof cell === 'string') return cell.trim() !== '';
                    if (typeof cell === 'number') return cell !== 0;
                    return Boolean(cell);
                });
            });
        }

        console.log('✅ تم تنظيف البيانات');

    } catch (error) {
        console.error('❌ خطأ في تنظيف البيانات:', error);
    }
}

// دالة فحص حالة الاتصال بالإنترنت
function checkConnectionAndSave() {
    if (navigator.onLine) {
        console.log('🌐 الاتصال متاح - حفظ في قاعدة البيانات');
        saveToDatabaseAndLocal();
    } else {
        console.log('📴 لا يوجد اتصال - حفظ في localStorage فقط');
        saveDataToLocalStorage();
        showSaveNotification('تم الحفظ محلياً (لا يوجد اتصال)', 'warning');
    }
}

// إضافة event listeners لحالة الاتصال
window.addEventListener('online', () => {
    console.log('🌐 تم استعادة الاتصال - محاولة حفظ البيانات');
    showSaveNotification('تم استعادة الاتصال', 'success');

    // حفظ البيانات الحالية في قاعدة البيانات
    setTimeout(() => {
        saveToDatabaseAndLocal();
    }, 1000);

    // محاولة استعادة أي بيانات محفوظة محلياً لم يتم رفعها
    setTimeout(() => {
        syncLocalDataToDatabase();
    }, 3000);
});

window.addEventListener('offline', () => {
    console.log('📴 فقدان الاتصال - سيتم الحفظ محلياً فقط');
    showSaveNotification('فقدان الاتصال - الحفظ محلياً', 'warning');
});

// دالة مزامنة البيانات المحلية مع قاعدة البيانات
async function syncLocalDataToDatabase() {
    try {
        console.log('🔄 مزامنة البيانات المحلية مع قاعدة البيانات...');

        const localData = localStorage.getItem('dutyFormData');
        if (localData) {
            const data = JSON.parse(localData);

            // التحقق من وجود بيانات جديدة لم يتم حفظها
            const lastSyncTime = localStorage.getItem('lastDatabaseSync');
            const dataTimestamp = data.timestamp;

            if (!lastSyncTime || new Date(dataTimestamp) > new Date(lastSyncTime)) {
                console.log('📤 رفع البيانات المحلية إلى قاعدة البيانات...');
                await saveToDatabase(data);

                // تحديث وقت آخر مزامنة
                localStorage.setItem('lastDatabaseSync', new Date().toISOString());
                console.log('✅ تم رفع البيانات المحلية بنجاح');
            } else {
                console.log('ℹ️ البيانات محدثة - لا حاجة للمزامنة');
            }
        }

    } catch (error) {
        console.error('❌ خطأ في مزامنة البيانات:', error);
    }
}

// تحسين دالة تحميل البيانات من قاعدة البيانات أولاً
async function loadDataInBackground() {
    try {
        console.log('🔄 تحميل البيانات من قاعدة البيانات...');

        // فحص ما إذا كانت البيانات قد تم مسحها مؤخراً
        const dataCleared = localStorage.getItem('dutyDataCleared');
        if (dataCleared) {
            const clearTime = parseInt(dataCleared);
            const now = Date.now();
            // إذا تم المسح خلال آخر 5 دقائق، لا تحمل البيانات
            if (now - clearTime < 300000) { // 5 دقائق
                console.log('⚠️ تم مسح البيانات مؤخراً - تجاهل التحميل');
                return;
            }
        }

        // محاولة تحميل من قاعدة البيانات أولاً
        const databaseData = await loadFromDatabase();

        if (databaseData) {
            console.log('✅ تم تحميل البيانات من قاعدة البيانات');
            setTimeout(() => {
                applyLoadedDataToTables(databaseData);
            }, 1500);
            return;
        }

        // إذا لم توجد بيانات في قاعدة البيانات، حمل من localStorage
        console.log('🔄 تحميل البيانات من localStorage...');
        const localData = localStorage.getItem('dutyFormData');
        if (localData) {
            const data = JSON.parse(localData);
            console.log('✅ تم تحميل البيانات من localStorage');

            // تطبيق البيانات المحفوظة
            setTimeout(() => {
                applyLoadedDataToTables(data);
            }, 1500);

            // حفظ البيانات في قاعدة البيانات للمرة القادمة
            setTimeout(() => {
                saveToDatabaseAndLocal();
            }, 3000);
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات:', error);
    }
}

// دالة تحميل البيانات من قاعدة البيانات
async function loadFromDatabase() {
    try {
        console.log('📡 تحميل البيانات من قاعدة البيانات...');

        // تحميل البيانات الرئيسية
        const response = await fetch('/duties/api/load-duty-data');

        if (response.ok) {
            const result = await response.json();

            if (result.success && result.data) {
                console.log('✅ تم تحميل البيانات الرئيسية من قاعدة البيانات');

                // تحميل بيانات الدوريات
                const patrolResponse = await fetch('/duties/api/load-patrol-data');
                if (patrolResponse.ok) {
                    const patrolResult = await patrolResponse.json();
                    if (patrolResult.success && patrolResult.data) {
                        result.data.patrolData = patrolResult.data;
                        console.log('✅ تم تحميل بيانات الدوريات');
                    }
                }

                // تحميل بيانات المناوبين
                const shiftsResponse = await fetch('/duties/api/load-shifts-data');
                if (shiftsResponse.ok) {
                    const shiftsResult = await shiftsResponse.json();
                    if (shiftsResult.success && shiftsResult.data) {
                        result.data.shiftsData = shiftsResult.data;
                        console.log('✅ تم تحميل بيانات المناوبين');
                    }
                }

                return result.data;
            }
        }

        console.log('ℹ️ لا توجد بيانات محفوظة في قاعدة البيانات');
        return null;

    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات من قاعدة البيانات:', error);
        return null;
    }
}

// دالة تطبيق البيانات المحفوظة على الجداول
function applyLoadedDataToTables(data) {
    try {
        console.log('🔄 تطبيق البيانات المحفوظة...');

        // تطبيق بيانات النموذج أولاً
        if (data.formData) {
            Object.keys(data.formData).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.value = data.formData[key];
                    console.log(`📝 تطبيق ${key}: ${data.formData[key]}`);
                }
            });
            console.log('✅ تم تطبيق بيانات النموذج');
        }

        // تطبيق بيانات الجدول الرئيسي
        if (data.dutyData && data.dutyData.rows) {
            dutyData.headers = data.dutyData.headers || dutyData.headers;
            dutyData.rows = data.dutyData.rows;
            generateTableHeader();
            generateTableBody();
            console.log('✅ تم تطبيق بيانات الجدول الرئيسي');

            // تطبيق المواقع والأفراد المحفوظة فوراً وبشكل متكرر للتأكد
            setTimeout(() => applySelectedLocationsAndPersonnel(), 500);
            setTimeout(() => applySelectedLocationsAndPersonnel(), 1500);
            setTimeout(() => applySelectedLocationsAndPersonnel(), 3000);
        }

        // تطبيق بيانات جدول الدوريات
        if (data.patrolData && data.patrolData.rows) {
            patrolData.headers = data.patrolData.headers || patrolData.headers;
            patrolData.rows = data.patrolData.rows;
            generatePatrolTableHeader();
            generatePatrolTableBody();
            console.log('✅ تم تطبيق بيانات جدول الدوريات');

            // تطبيق المواقع والأفراد المحفوظة للدوريات
            setTimeout(() => {
                applyPatrolLocationsAndPersonnel();
            }, 700);
        }

        // تطبيق بيانات جدول المناوبين
        if (data.shiftsData && data.shiftsData.rows) {
            shiftsData.headers = data.shiftsData.headers || shiftsData.headers;
            shiftsData.rows = data.shiftsData.rows;
            generateShiftsTableHeader();
            generateShiftsTableBody();
            console.log('✅ تم تطبيق بيانات جدول المناوبين');

            // تطبيق المواقع والأفراد المحفوظة للمناوبين
            setTimeout(() => {
                applyShiftsLocationsAndPersonnel();
            }, 900);
        }

        // تحديث قوائم المواقع بعد تطبيق البيانات
        setTimeout(() => {
            updateAllLocationSelects();
            console.log('✅ تم تحديث قوائم المواقع');
        }, 1200);

        // إظهار إشعار نجاح التحميل
        showSaveNotification('تم تحميل البيانات بنجاح', 'success');

    } catch (error) {
        console.error('❌ خطأ في تطبيق البيانات:', error);
        showSaveNotification('خطأ في تطبيق البيانات', 'error');
    }
}

console.log('✅ تم تحميل ملف duties-simple.js بنجاح');
