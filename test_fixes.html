<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإصلاحات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار إصلاحات كشف الواجبات</h1>
        
        <div class="test-section">
            <h3>📋 اختبار حفظ واستعادة البيانات</h3>
            <div>
                <label>اختبار الموقع:</label>
                <select id="testLocation">
                    <option value="">اختر موقع</option>
                    <option value="1">البوابة الرئيسية</option>
                    <option value="2">البوابة الشرقية</option>
                    <option value="3">المستودعات</option>
                </select>
            </div>
            <div style="margin: 10px 0;">
                <label>اختبار الفرد:</label>
                <select id="testPersonnel">
                    <option value="">اختر فرد</option>
                    <option value="أحمد محمد">أحمد محمد</option>
                    <option value="محمد أحمد">محمد أحمد</option>
                </select>
            </div>
            <div>
                <button onclick="testSave()">💾 اختبار الحفظ</button>
                <button onclick="testLoad()">📥 اختبار التحميل</button>
                <button onclick="clearTest()">🗑️ مسح البيانات</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📅 اختبار التاريخ الهجري</h3>
            <div>
                <label>التاريخ الهجري:</label>
                <input type="text" id="hijriTest" readonly style="width: 200px;">
                <button onclick="testHijriDate()">🔄 تحديث التاريخ</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 سجل الاختبارات</h3>
            <div id="testLog" class="log">
                جاري تحميل الاختبارات...
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔗 روابط سريعة</h3>
            <button onclick="openDutiesPage()">📋 فتح صفحة كشف الواجبات</button>
            <button onclick="checkAPI()">🔍 فحص API</button>
        </div>
    </div>

    <script>
        let testData = {};
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const time = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                success: '#28a745',
                error: '#dc3545', 
                warning: '#ffc107',
                info: '#17a2b8'
            };
            
            const logEntry = `<div style="color: ${colors[type]}; margin: 2px 0;">
                <strong>${time}</strong> - ${message}
            </div>`;
            
            logElement.innerHTML += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function testSave() {
            try {
                const location = document.getElementById('testLocation').value;
                const personnel = document.getElementById('testPersonnel').value;
                
                testData = {
                    location: location,
                    personnel: personnel,
                    timestamp: new Date().toISOString()
                };
                
                localStorage.setItem('testDutyData', JSON.stringify(testData));
                log(`✅ تم حفظ البيانات: موقع=${location}, فرد=${personnel}`, 'success');
                
            } catch (error) {
                log(`❌ خطأ في الحفظ: ${error.message}`, 'error');
            }
        }
        
        function testLoad() {
            try {
                const savedData = localStorage.getItem('testDutyData');
                
                if (savedData) {
                    const data = JSON.parse(savedData);
                    
                    document.getElementById('testLocation').value = data.location || '';
                    document.getElementById('testPersonnel').value = data.personnel || '';
                    
                    log(`✅ تم تحميل البيانات: موقع=${data.location}, فرد=${data.personnel}`, 'success');
                    log(`⏰ وقت الحفظ: ${new Date(data.timestamp).toLocaleString('ar-SA')}`, 'info');
                } else {
                    log('⚠️ لا توجد بيانات محفوظة', 'warning');
                }
                
            } catch (error) {
                log(`❌ خطأ في التحميل: ${error.message}`, 'error');
            }
        }
        
        function clearTest() {
            try {
                localStorage.removeItem('testDutyData');
                document.getElementById('testLocation').value = '';
                document.getElementById('testPersonnel').value = '';
                log('🗑️ تم مسح البيانات', 'warning');
                
            } catch (error) {
                log(`❌ خطأ في المسح: ${error.message}`, 'error');
            }
        }
        
        function testHijriDate() {
            try {
                // حساب التاريخ الهجري بطريقة مبسطة
                const today = new Date();
                const hijriDate = calculateSimpleHijri(today);
                
                document.getElementById('hijriTest').value = hijriDate;
                log(`📅 التاريخ الهجري: ${hijriDate}`, 'success');
                
            } catch (error) {
                log(`❌ خطأ في حساب التاريخ الهجري: ${error.message}`, 'error');
            }
        }
        
        function calculateSimpleHijri(date) {
            // حساب مبسط للتاريخ الهجري
            const baseDate = new Date('2025-07-17'); // 22 محرم 1447
            const daysDiff = Math.floor((date - baseDate) / (1000 * 60 * 60 * 24));
            
            const hijriMonths = [
                'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
                'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
            ];
            
            let hijriDay = 22 + daysDiff;
            let hijriMonth = 1; // محرم
            let hijriYear = 1447;
            
            // تعديل بسيط للأيام والشهور
            while (hijriDay > 30) {
                hijriDay -= 30;
                hijriMonth++;
                if (hijriMonth > 12) {
                    hijriMonth = 1;
                    hijriYear++;
                }
            }
            
            while (hijriDay < 1) {
                hijriMonth--;
                if (hijriMonth < 1) {
                    hijriMonth = 12;
                    hijriYear--;
                }
                hijriDay += 30;
            }
            
            return `${hijriDay.toString().padStart(2, '0')} ${hijriMonths[hijriMonth - 1]} ${hijriYear}هـ`;
        }
        
        function openDutiesPage() {
            window.open('/duties/', '_blank');
            log('🔗 تم فتح صفحة كشف الواجبات في تبويب جديد', 'info');
        }
        
        async function checkAPI() {
            try {
                log('🔍 فحص API...', 'info');
                
                // فحص API التاريخ الهجري
                const response = await fetch('/reports/api/hijri-date');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        log(`✅ API التاريخ الهجري يعمل: ${data.hijri_formatted}`, 'success');
                    } else {
                        log('⚠️ API التاريخ الهجري لا يعمل بشكل صحيح', 'warning');
                    }
                } else {
                    log(`❌ خطأ في API التاريخ الهجري: ${response.status}`, 'error');
                }
                
            } catch (error) {
                log(`❌ خطأ في فحص API: ${error.message}`, 'error');
            }
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 تم تحميل صفحة الاختبار', 'success');
            
            // اختبار localStorage
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                log('✅ localStorage يعمل بشكل صحيح', 'success');
            } catch (error) {
                log('❌ localStorage لا يعمل', 'error');
            }
            
            // اختبار التاريخ الهجري
            testHijriDate();
            
            // محاولة تحميل البيانات المحفوظة
            setTimeout(testLoad, 1000);
        });
    </script>
</body>
</html>
