<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحسينات الأفراد</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .feature-section {
            background: rgba(255,255,255,0.1);
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
            border-left: 5px solid;
        }
        .success { 
            background: rgba(40, 167, 69, 0.8); 
            border-left-color: #28a745;
        }
        .error { 
            background: rgba(220, 53, 69, 0.8); 
            border-left-color: #dc3545;
        }
        .warning { 
            background: rgba(255, 193, 7, 0.8); 
            color: #000; 
            border-left-color: #ffc107;
        }
        .info { 
            background: rgba(23, 162, 184, 0.8); 
            border-left-color: #17a2b8;
        }
        button {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
        }
        .log {
            background: rgba(0,0,0,0.4);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature-card {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .emoji {
            font-size: 2em;
            margin-bottom: 10px;
        }
        h1, h2, h3 {
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .code-example {
            background: rgba(0,0,0,0.4);
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            border-left: 3px solid #17a2b8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 تحسينات نظام الأفراد</h1>
        <p>منع التكرار وعرض الرتبة قبل الاسم في جميع الجداول</p>
        
        <div class="feature-grid">
            <div class="feature-card">
                <div class="emoji">🚫</div>
                <h3>منع التكرار</h3>
                <p>منع اختيار نفس الفرد في أكثر من مكان</p>
            </div>
            <div class="feature-card">
                <div class="emoji">🏅</div>
                <h3>عرض الرتبة</h3>
                <p>الرتبة تظهر قبل الاسم دائماً</p>
            </div>
            <div class="feature-card">
                <div class="emoji">🔄</div>
                <h3>تحديث تلقائي</h3>
                <p>تحديث جميع القوائم عند تغيير الاختيار</p>
            </div>
            <div class="feature-card">
                <div class="emoji">📋</div>
                <h3>جميع الجداول</h3>
                <p>يعمل في الرئيسي والدوريات والمناوبين</p>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>✅ الميزات الجديدة</h3>
            <div class="status success">
                <strong>تم إضافة الميزات التالية:</strong>
                <ul style="margin: 10px 0; padding-right: 20px;">
                    <li>🚫 منع التكرار عبر جميع الجداول</li>
                    <li>🏅 عرض الرتبة قبل الاسم (مثل: رقيب فارس علي)</li>
                    <li>🔄 تحديث تلقائي لجميع القوائم عند تغيير الاختيار</li>
                    <li>📊 دعم جميع الجداول (الرئيسي، الدوريات، المناوبين)</li>
                    <li>💾 حفظ تلقائي للاختيارات</li>
                    <li>🎯 بيانات افتراضية محسنة مع الرتب</li>
                </ul>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>🔧 التحسينات التقنية</h3>
            <div class="code-example">
<strong>1. دالة تنسيق الأسماء:</strong>
function formatPersonName(person) {
    const rank = person.rank || '';
    const name = person.name || '';
    return rank && name ? `${rank} ${name}` : name;
}

<strong>2. منع التكرار الشامل:</strong>
function getAllSelectedPersonnel() {
    // جمع جميع الأفراد المختارين من جميع الجداول
    const selectedIds = new Set();
    // فحص الجدول الرئيسي + الدوريات + المناوبين
    return Array.from(selectedIds);
}

<strong>3. تحديث ذكي للقوائم:</strong>
function updatePersonnelSelectsInRow(rowIndex, personnel, tableType) {
    // منع التكرار + عرض الرتبة + تحديث تلقائي
}

<strong>4. بيانات افتراضية محسنة:</strong>
{id: 1, name: 'أحمد محمد', rank: 'رقيب', national_id: '1234567890'}
// النتيجة: "رقيب أحمد محمد"
            </div>
        </div>
        
        <div class="feature-section">
            <h3>🧪 اختبار الميزات</h3>
            <div>
                <button onclick="openDuties()" class="primary">📋 فتح كشف الواجبات</button>
                <button onclick="testDuplication()">🚫 اختبار منع التكرار</button>
                <button onclick="testRankDisplay()">🏅 اختبار عرض الرتبة</button>
                <button onclick="testAllTables()">📊 اختبار جميع الجداول</button>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>📊 نتائج الاختبار</h3>
            <div id="testStatus">
                <div class="status info">⏳ جاري انتظار بدء الاختبار...</div>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>📋 سجل الاختبار</h3>
            <div id="testLog" class="log">
                جاري تحميل أدوات اختبار تحسينات الأفراد...
            </div>
            <button onclick="clearLog()">🧹 مسح السجل</button>
        </div>
        
        <div class="feature-section">
            <h3>📖 تعليمات الاختبار</h3>
            <div class="code-example">
<strong>خطوات اختبار منع التكرار:</strong>

1. افتح صفحة كشف الواجبات
2. في الجدول الرئيسي:
   - اختر موقع "البوابة الرئيسية"
   - اختر "رقيب أحمد محمد" في العمود الأول
   - لاحظ أنه لن يظهر في العمود الثاني

3. في جدول الدوريات:
   - اختر موقع "البوابة الرئيسية"
   - لاحظ أن "رقيب أحمد محمد" لن يظهر (مختار في الجدول الرئيسي)

4. في جدول المناوبين:
   - اختر موقع "البوابة الرئيسية"
   - لاحظ أن "رقيب أحمد محمد" لن يظهر أيضاً

<strong>خطوات اختبار عرض الرتبة:</strong>

1. افتح أي قائمة أفراد
2. لاحظ أن الأسماء تظهر بالتنسيق:
   - "رقيب أحمد محمد"
   - "عريف محمد أحمد"
   - "جندي أول عبدالله سعد"
   - إلخ...

<strong>النتيجة المتوقعة:</strong>
✅ لا يمكن اختيار نفس الفرد في أكثر من مكان
✅ الرتبة تظهر قبل الاسم دائماً
✅ القوائم تتحدث تلقائياً عند تغيير الاختيار
✅ النظام يعمل في جميع الجداول
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const time = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8'
            };
            
            const entry = `<div style="color: ${colors[type]}; margin: 3px 0; padding: 8px; border-left: 3px solid ${colors[type]}; background: rgba(255,255,255,0.1); border-radius: 4px;">
                <strong>${time}</strong> - ${message}
            </div>`;
            
            logElement.innerHTML += entry;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(message, type) {
            const statusElement = document.getElementById('testStatus');
            statusElement.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function openDuties() {
            window.open('/duties/', '_blank');
            log('🔗 تم فتح صفحة كشف الواجبات', 'info');
            updateStatus('تم فتح صفحة كشف الواجبات - ابدأ الاختبار', 'info');
        }
        
        function testDuplication() {
            log('🚫 تعليمات اختبار منع التكرار:', 'info');
            log('1. اختر موقع في الجدول الرئيسي', 'info');
            log('2. اختر فرد في العمود الأول', 'info');
            log('3. تحقق من عدم ظهوره في العمود الثاني', 'info');
            log('4. اختر نفس الموقع في جدول الدوريات', 'info');
            log('5. تحقق من عدم ظهور الفرد المختار', 'info');
            log('6. كرر نفس الاختبار مع جدول المناوبين', 'info');
            updateStatus('جاري اختبار منع التكرار...', 'warning');
        }
        
        function testRankDisplay() {
            log('🏅 تعليمات اختبار عرض الرتبة:', 'info');
            log('1. افتح أي قائمة أفراد في أي جدول', 'info');
            log('2. لاحظ تنسيق الأسماء:', 'info');
            log('   - "رقيب أحمد محمد"', 'success');
            log('   - "عريف محمد أحمد"', 'success');
            log('   - "جندي أول عبدالله سعد"', 'success');
            log('   - "رقيب أول سعد عبدالله"', 'success');
            log('   - "وكيل رقيب خالد فهد"', 'success');
            log('3. تأكد من أن الرتبة تظهر قبل الاسم دائماً', 'info');
            updateStatus('جاري اختبار عرض الرتبة...', 'warning');
        }
        
        function testAllTables() {
            log('📊 اختبار شامل لجميع الجداول:', 'info');
            updateStatus('جاري اختبار جميع الجداول...', 'info');
            
            const tables = [
                'الجدول الرئيسي (كشف الواجبات)',
                'جدول الدوريات',
                'جدول المناوبين'
            ];
            
            tables.forEach((table, index) => {
                setTimeout(() => {
                    log(`${index + 1}. اختبار ${table}:`, 'info');
                    log(`   - اختر موقع`, 'info');
                    log(`   - تحقق من عرض الرتبة قبل الاسم`, 'info');
                    log(`   - اختر فرد`, 'info');
                    log(`   - تحقق من منع التكرار`, 'info');
                    
                    if (index === tables.length - 1) {
                        setTimeout(() => {
                            log('✅ تم إكمال اختبار جميع الجداول', 'success');
                            updateStatus('تم إكمال اختبار جميع الجداول', 'success');
                        }, 1000);
                    }
                }, (index + 1) * 2000);
            });
        }
        
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
            log('🧹 تم مسح السجل', 'info');
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 تم تحميل أداة اختبار تحسينات الأفراد', 'success');
            updateStatus('أداة الاختبار جاهزة', 'info');
            
            log('🎯 الميزات الجديدة المضافة:', 'info');
            log('✅ منع التكرار عبر جميع الجداول', 'success');
            log('✅ عرض الرتبة قبل الاسم (رقيب فارس علي)', 'success');
            log('✅ تحديث تلقائي لجميع القوائم', 'success');
            log('✅ دعم جميع الجداول (الرئيسي، الدوريات، المناوبين)', 'success');
            log('✅ حفظ تلقائي للاختيارات', 'success');
            log('✅ بيانات افتراضية محسنة مع الرتب', 'success');
            
            log('📋 النظام جاهز للاختبار!', 'info');
            log('🔍 ابدأ بفتح صفحة كشف الواجبات واختبار الميزات الجديدة', 'warning');
        });
    </script>
</body>
</html>
