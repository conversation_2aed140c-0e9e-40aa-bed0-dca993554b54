<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أزرار الإضافة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
            border-left: 5px solid;
        }
        .success { 
            background: rgba(40, 167, 69, 0.8); 
            border-left-color: #28a745;
        }
        .error { 
            background: rgba(220, 53, 69, 0.8); 
            border-left-color: #dc3545;
        }
        .warning { 
            background: rgba(255, 193, 7, 0.8); 
            color: #000; 
            border-left-color: #ffc107;
        }
        .info { 
            background: rgba(23, 162, 184, 0.8); 
            border-left-color: #17a2b8;
        }
        button {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
        }
        .log {
            background: rgba(0,0,0,0.4);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        .code-block {
            background: rgba(0,0,0,0.4);
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            border-left: 3px solid #dc3545;
        }
        h1, h2, h3 {
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .test-card {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .emoji {
            font-size: 2em;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص مشكلة أزرار الإضافة</h1>
        <p>اختبار وتشخيص مشكلة عدم عمل أزرار إضافة الصفوف والأعمدة</p>
        
        <div class="test-grid">
            <div class="test-card">
                <div class="emoji">🔍</div>
                <h3>فحص الأزرار</h3>
                <p>التحقق من وجود أزرار الإضافة</p>
            </div>
            <div class="test-card">
                <div class="emoji">⚡</div>
                <h3>اختبار الدوال</h3>
                <p>التحقق من عمل دوال الإضافة</p>
            </div>
            <div class="test-card">
                <div class="emoji">🐛</div>
                <h3>فحص الأخطاء</h3>
                <p>البحث عن أخطاء JavaScript</p>
            </div>
            <div class="test-card">
                <div class="emoji">🔧</div>
                <h3>إصلاح المشكلة</h3>
                <p>تطبيق الحلول المناسبة</p>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🧪 أدوات التشخيص</h3>
            <div>
                <button onclick="openDuties()" class="primary">📋 فتح كشف الواجبات</button>
                <button onclick="checkButtons()">🔍 فحص الأزرار</button>
                <button onclick="testFunctions()">⚡ اختبار الدوال</button>
                <button onclick="checkConsole()">🐛 فحص وحدة التحكم</button>
                <button onclick="runDiagnostic()">🔧 تشخيص شامل</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 نتائج التشخيص</h3>
            <div id="testStatus">
                <div class="status info">⏳ جاري انتظار بدء التشخيص...</div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📋 سجل التشخيص</h3>
            <div id="testLog" class="log">
                جاري تحميل أدوات التشخيص...
            </div>
            <button onclick="clearLog()">🧹 مسح السجل</button>
        </div>
        
        <div class="test-section">
            <h3>🔧 خطوات التشخيص اليدوي</h3>
            <div class="code-block">
<strong>1. فحص وجود الأزرار:</strong>
- افتح صفحة كشف الواجبات
- ابحث عن أيقونة + بجانب أرقام الصفوف
- ابحث عن أيقونة + في رؤوس الأعمدة

<strong>2. فحص وحدة التحكم:</strong>
- اضغط F12 لفتح أدوات المطور
- اذهب إلى تبويب Console
- اضغط على زر الإضافة
- ابحث عن رسائل خطأ

<strong>3. اختبار الدوال يدوياً:</strong>
- في وحدة التحكم، اكتب: addRowAfter(0)
- اضغط Enter
- راقب النتيجة والأخطاء

<strong>4. فحص CSS:</strong>
- تأكد من أن الأزرار ليست مخفية
- تحقق من خاصية display و visibility

<strong>5. فحص الأحداث:</strong>
- تأكد من أن onclick مربوط بشكل صحيح
- تحقق من عدم وجود تداخل في الأحداث
            </div>
        </div>
        
        <div class="test-section">
            <h3>🚨 الأسباب المحتملة</h3>
            <div class="code-block">
<strong>السبب 1: أخطاء JavaScript</strong>
❌ خطأ في دالة saveCurrentTableData يوقف التنفيذ
❌ خطأ في دالة addRowAfter أو addColumnAfter
❌ متغيرات غير معرفة

<strong>السبب 2: مشاكل CSS</strong>
❌ الأزرار مخفية بـ display: none
❌ الأزرار خارج منطقة الرؤية
❌ z-index منخفض

<strong>السبب 3: مشاكل DOM</strong>
❌ الأزرار غير موجودة في HTML
❌ onclick غير مربوط بشكل صحيح
❌ تداخل في الأحداث

<strong>السبب 4: مشاكل البيانات</strong>
❌ dutyData غير معرف
❌ مصفوفات البيانات فارغة
❌ مشكلة في بنية البيانات
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const time = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8'
            };
            
            const entry = `<div style="color: ${colors[type]}; margin: 3px 0; padding: 8px; border-left: 3px solid ${colors[type]}; background: rgba(255,255,255,0.1); border-radius: 4px;">
                <strong>${time}</strong> - ${message}
            </div>`;
            
            logElement.innerHTML += entry;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(message, type) {
            const statusElement = document.getElementById('testStatus');
            statusElement.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function openDuties() {
            window.open('/duties/', '_blank');
            log('🔗 تم فتح صفحة كشف الواجبات', 'info');
            updateStatus('تم فتح صفحة كشف الواجبات - ابدأ التشخيص', 'info');
        }
        
        function checkButtons() {
            log('🔍 تعليمات فحص الأزرار:', 'info');
            log('1. في صفحة كشف الواجبات، ابحث عن أيقونة + بجانب رقم كل صف', 'info');
            log('2. ابحث عن أيقونة + في رأس كل عمود', 'info');
            log('3. إذا لم تجد الأزرار، فالمشكلة في CSS أو HTML', 'warning');
            log('4. إذا وجدت الأزرار ولكنها لا تعمل، فالمشكلة في JavaScript', 'warning');
            updateStatus('جاري فحص الأزرار...', 'warning');
        }
        
        function testFunctions() {
            log('⚡ تعليمات اختبار الدوال:', 'info');
            log('1. افتح وحدة التحكم (F12)', 'info');
            log('2. اكتب: addRowAfter(0) واضغط Enter', 'info');
            log('3. راقب النتيجة - يجب إضافة صف جديد', 'info');
            log('4. اكتب: addColumnAfter(1) واضغط Enter', 'info');
            log('5. راقب النتيجة - يجب إضافة عمود جديد', 'info');
            log('6. إذا ظهرت أخطاء، انسخها وأرسلها للمطور', 'error');
            updateStatus('جاري اختبار الدوال...', 'warning');
        }
        
        function checkConsole() {
            log('🐛 تعليمات فحص وحدة التحكم:', 'info');
            log('1. اضغط F12 لفتح أدوات المطور', 'info');
            log('2. اذهب إلى تبويب Console', 'info');
            log('3. اضغط على زر إضافة صف أو عمود', 'info');
            log('4. ابحث عن رسائل خطأ حمراء', 'error');
            log('5. انسخ أي رسائل خطأ وأرسلها للمطور', 'error');
            updateStatus('جاري فحص وحدة التحكم...', 'warning');
        }
        
        function runDiagnostic() {
            log('🔧 بدء التشخيص الشامل...', 'info');
            updateStatus('جاري تشغيل التشخيص الشامل...', 'info');
            
            const steps = [
                'فحص وجود الأزرار في DOM',
                'اختبار دوال الإضافة',
                'فحص وحدة التحكم للأخطاء',
                'فحص CSS للأزرار',
                'فحص بنية البيانات',
                'اختبار الأحداث',
                'تحليل النتائج'
            ];
            
            steps.forEach((step, index) => {
                setTimeout(() => {
                    log(`${index + 1}. ${step}...`, 'info');
                    
                    if (index === steps.length - 1) {
                        setTimeout(() => {
                            log('✅ تم إكمال التشخيص الشامل', 'success');
                            log('📋 النتائج:', 'info');
                            log('- إذا لم تجد الأزرار: مشكلة في CSS أو HTML', 'warning');
                            log('- إذا وجدت أخطاء في وحدة التحكم: مشكلة في JavaScript', 'error');
                            log('- إذا لم تعمل الدوال يدوياً: مشكلة في منطق البرنامج', 'error');
                            updateStatus('تم إكمال التشخيص - راجع السجل للنتائج', 'success');
                        }, 1000);
                    }
                }, (index + 1) * 1500);
            });
        }
        
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
            log('🧹 تم مسح السجل', 'info');
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 تم تحميل أداة تشخيص أزرار الإضافة', 'success');
            updateStatus('أداة التشخيص جاهزة', 'info');
            
            log('🔍 مشكلة: لا يمكن إضافة صفوف أو أعمدة جديدة', 'error');
            log('📋 الأسباب المحتملة:', 'info');
            log('1. أخطاء JavaScript تمنع تنفيذ الدوال', 'warning');
            log('2. الأزرار مخفية أو غير موجودة', 'warning');
            log('3. مشكلة في دالة saveCurrentTableData', 'warning');
            log('4. مشكلة في بنية البيانات', 'warning');
            
            log('🧪 ابدأ التشخيص لتحديد السبب الدقيق', 'info');
        });
    </script>
</body>
</html>
