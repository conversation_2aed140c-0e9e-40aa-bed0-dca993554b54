#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاحات كشف الواجبات
"""

import requests
import json
import time

def test_duties_page():
    """اختبار صفحة كشف الواجبات"""
    try:
        print("🧪 اختبار صفحة كشف الواجبات...")
        
        # اختبار الوصول للصفحة
        response = requests.get('http://localhost:5000/duties/')
        
        if response.status_code == 200:
            print("✅ الصفحة تعمل بشكل صحيح")
            
            # فحص وجود العناصر المهمة
            content = response.text
            
            checks = [
                ('كشف الواجبات', 'عنوان الصفحة'),
                ('hijriDate', 'حقل التاريخ الهجري'),
                ('gregorianDate', 'حقل التاريخ الميلادي'),
                ('dayName', 'حقل اليوم'),
                ('receiptNumber', 'حقل رقم الكشف'),
                ('dutyTable', 'جدول الواجبات'),
                ('patrolTable', 'جدول الدوريات'),
                ('shiftsTable', 'جدول المناوبين'),
                ('saveToDatabaseAndLocal', 'دالة الحفظ المحسنة'),
                ('applySelectedLocationsAndPersonnel', 'دالة تطبيق المواقع'),
                ('getCurrentHijriDate', 'دالة التاريخ الهجري')
            ]
            
            for check_item, description in checks:
                if check_item in content:
                    print(f"✅ {description}: موجود")
                else:
                    print(f"❌ {description}: مفقود")
            
            return True
        else:
            print(f"❌ خطأ في الوصول للصفحة: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الصفحة: {str(e)}")
        return False

def test_api_endpoints():
    """اختبار نقاط API"""
    try:
        print("\n🧪 اختبار نقاط API...")
        
        endpoints = [
            ('/duties/api/get-locations', 'GET', 'قائمة المواقع'),
            ('/duties/api/load-duty-data', 'GET', 'تحميل بيانات الواجبات'),
            ('/duties/api/load-patrol-data', 'GET', 'تحميل بيانات الدوريات'),
            ('/duties/api/load-shifts-data', 'GET', 'تحميل بيانات المناوبين'),
            ('/reports/api/hijri-date', 'GET', 'التاريخ الهجري')
        ]
        
        for endpoint, method, description in endpoints:
            try:
                if method == 'GET':
                    response = requests.get(f'http://localhost:5000{endpoint}')
                
                if response.status_code == 200:
                    print(f"✅ {description}: يعمل")
                else:
                    print(f"⚠️ {description}: كود {response.status_code}")
                    
            except Exception as e:
                print(f"❌ {description}: خطأ - {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار API: {str(e)}")
        return False

def test_hijri_date():
    """اختبار التاريخ الهجري"""
    try:
        print("\n🧪 اختبار التاريخ الهجري...")
        
        response = requests.get('http://localhost:5000/reports/api/hijri-date')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                hijri_date = data.get('hijri_formatted')
                gregorian_date = data.get('gregorian_formatted')
                day_name = data.get('day_name')
                
                print(f"✅ التاريخ الهجري: {hijri_date}")
                print(f"✅ التاريخ الميلادي: {gregorian_date}")
                print(f"✅ اليوم: {day_name}")
                
                return True
            else:
                print(f"❌ فشل في الحصول على التاريخ: {data}")
                return False
        else:
            print(f"❌ خطأ في API التاريخ: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التاريخ الهجري: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إصلاحات كشف الواجبات")
    print("=" * 50)
    
    # انتظار تشغيل الخادم
    print("⏳ انتظار تشغيل الخادم...")
    time.sleep(2)
    
    # اختبار الصفحة
    page_test = test_duties_page()
    
    # اختبار API
    api_test = test_api_endpoints()
    
    # اختبار التاريخ الهجري
    hijri_test = test_hijri_date()
    
    # النتيجة النهائية
    print("\n" + "=" * 50)
    print("📊 نتائج الاختبار:")
    print(f"   📄 صفحة كشف الواجبات: {'✅ نجح' if page_test else '❌ فشل'}")
    print(f"   🔗 نقاط API: {'✅ نجح' if api_test else '❌ فشل'}")
    print(f"   📅 التاريخ الهجري: {'✅ نجح' if hijri_test else '❌ فشل'}")
    
    if page_test and api_test and hijri_test:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ النظام جاهز للاستخدام")
    else:
        print("\n⚠️ بعض الاختبارات فشلت")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")

if __name__ == "__main__":
    main()
