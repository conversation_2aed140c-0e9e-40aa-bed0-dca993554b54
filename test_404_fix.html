<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح خطأ 404</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
            border-left: 5px solid;
        }
        .success { 
            background: rgba(40, 167, 69, 0.8); 
            border-left-color: #28a745;
        }
        .error { 
            background: rgba(220, 53, 69, 0.8); 
            border-left-color: #dc3545;
        }
        .warning { 
            background: rgba(255, 193, 7, 0.8); 
            color: #000; 
            border-left-color: #ffc107;
        }
        .info { 
            background: rgba(23, 162, 184, 0.8); 
            border-left-color: #17a2b8;
        }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        input, select {
            padding: 12px;
            border: none;
            border-radius: 8px;
            margin: 8px;
            background: rgba(255,255,255,0.9);
            color: #333;
            font-size: 14px;
        }
        .log {
            background: rgba(0,0,0,0.4);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .test-card {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }
        h1, h2, h3 {
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .emoji {
            font-size: 2em;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار إصلاح خطأ 404</h1>
        <p>فحص إصلاح مشكلة طلبات API بأسماء المواقع بدلاً من الأرقام</p>
        
        <div class="test-grid">
            <div class="test-card">
                <div class="emoji">✅</div>
                <h3>فحص الأرقام</h3>
                <p>التأكد من إرسال أرقام صحيحة</p>
            </div>
            <div class="test-card">
                <div class="emoji">🚫</div>
                <h3>منع النصوص</h3>
                <p>منع إرسال أسماء المواقع</p>
            </div>
            <div class="test-card">
                <div class="emoji">🔍</div>
                <h3>فحص شامل</h3>
                <p>فحص جميع أنواع البيانات</p>
            </div>
            <div class="test-card">
                <div class="emoji">🛡️</div>
                <h3>حماية API</h3>
                <p>منع طلبات خاطئة</p>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🧪 اختبار فحص معرفات المواقع</h3>
            <div>
                <label>اختبار معرف الموقع:</label>
                <input type="text" id="locationIdTest" placeholder="أدخل معرف موقع للاختبار">
                <button onclick="testLocationId()">🔍 فحص المعرف</button>
            </div>
            <div style="margin: 15px 0;">
                <button onclick="testValidIds()">✅ اختبار معرفات صحيحة</button>
                <button onclick="testInvalidIds()">❌ اختبار معرفات خاطئة</button>
                <button onclick="testArabicText()">🔤 اختبار نص عربي</button>
                <button onclick="clearResults()">🧹 مسح النتائج</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 نتائج الاختبار</h3>
            <div id="testResults">
                <div class="status info">⏳ جاري انتظار الاختبارات...</div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📋 سجل الاختبارات</h3>
            <div id="testLog" class="log">
                جاري تحميل سجل الاختبارات...
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔗 روابط سريعة</h3>
            <button onclick="openDuties()">📋 فتح كشف الواجبات</button>
            <button onclick="checkConsole()">🔍 فحص وحدة التحكم</button>
            <button onclick="testAPI()">📡 اختبار API</button>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const time = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8'
            };
            
            const entry = `<div style="color: ${colors[type]}; margin: 3px 0; padding: 8px; border-left: 3px solid ${colors[type]}; background: rgba(255,255,255,0.1); border-radius: 4px;">
                <strong>${time}</strong> - ${message}
            </div>`;
            
            logElement.innerHTML += entry;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateResults(message, type) {
            const resultsElement = document.getElementById('testResults');
            const statusClass = type;
            resultsElement.innerHTML = `<div class="status ${statusClass}">${message}</div>`;
        }
        
        // دالة محاكاة فحص معرف الموقع (نفس المنطق المستخدم في الكود الأصلي)
        function validateLocationId(locationId) {
            // فحص شامل لمعرف الموقع
            if (!locationId || locationId === '' || locationId === 'undefined' || locationId === 'null') {
                return { valid: false, reason: 'معرف فارغ أو غير صحيح' };
            }

            // تنظيف معرف الموقع
            const cleanLocationId = String(locationId).trim();
            
            // فحص وجود نص عربي أو أحرف غير رقمية
            if (/[\u0600-\u06FF]/.test(cleanLocationId)) {
                return { valid: false, reason: 'يحتوي على نص عربي' };
            }
            
            if (/[a-zA-Z]/.test(cleanLocationId)) {
                return { valid: false, reason: 'يحتوي على أحرف إنجليزية' };
            }

            // التحقق من أنه رقم صحيح
            if (isNaN(cleanLocationId)) {
                return { valid: false, reason: 'ليس رقم صحيح' };
            }

            const numericLocationId = parseInt(cleanLocationId);
            if (isNaN(numericLocationId) || numericLocationId <= 0) {
                return { valid: false, reason: 'رقم غير صالح أو أقل من أو يساوي صفر' };
            }
            
            return { valid: true, cleanId: numericLocationId };
        }
        
        function testLocationId() {
            const locationId = document.getElementById('locationIdTest').value;
            const result = validateLocationId(locationId);
            
            if (result.valid) {
                log(`✅ معرف صحيح: "${locationId}" -> ${result.cleanId}`, 'success');
                updateResults(`✅ المعرف "${locationId}" صحيح ويمكن استخدامه`, 'success');
            } else {
                log(`❌ معرف خاطئ: "${locationId}" - ${result.reason}`, 'error');
                updateResults(`❌ المعرف "${locationId}" خاطئ: ${result.reason}`, 'error');
            }
        }
        
        function testValidIds() {
            const validIds = ['1', '2', '10', '100', ' 5 ', '007'];
            let passCount = 0;
            
            log('🔍 اختبار معرفات صحيحة...', 'info');
            
            validIds.forEach(id => {
                const result = validateLocationId(id);
                if (result.valid) {
                    log(`✅ "${id}" -> ${result.cleanId}`, 'success');
                    passCount++;
                } else {
                    log(`❌ "${id}" فشل: ${result.reason}`, 'error');
                }
            });
            
            updateResults(`اختبار المعرفات الصحيحة: ${passCount}/${validIds.length} نجح`, 
                         passCount === validIds.length ? 'success' : 'warning');
        }
        
        function testInvalidIds() {
            const invalidIds = ['', null, undefined, '0', '-1', 'abc', '1.5', 'موقع1'];
            let failCount = 0;
            
            log('🔍 اختبار معرفات خاطئة...', 'info');
            
            invalidIds.forEach(id => {
                const result = validateLocationId(id);
                if (!result.valid) {
                    log(`✅ "${id}" رُفض بنجاح: ${result.reason}`, 'success');
                    failCount++;
                } else {
                    log(`❌ "${id}" لم يُرفض (خطأ!)`, 'error');
                }
            });
            
            updateResults(`اختبار المعرفات الخاطئة: ${failCount}/${invalidIds.length} رُفض بنجاح`, 
                         failCount === invalidIds.length ? 'success' : 'warning');
        }
        
        function testArabicText() {
            const arabicTexts = ['البوابة الرئيسية', 'موقع الحراسة', 'المستودعات', 'مبنى الإدارة'];
            let blockedCount = 0;
            
            log('🔍 اختبار النصوص العربية...', 'info');
            
            arabicTexts.forEach(text => {
                const result = validateLocationId(text);
                if (!result.valid) {
                    log(`✅ "${text}" تم حجبه بنجاح`, 'success');
                    blockedCount++;
                } else {
                    log(`❌ "${text}" لم يتم حجبه (خطأ!)`, 'error');
                }
            });
            
            updateResults(`اختبار النصوص العربية: ${blockedCount}/${arabicTexts.length} تم حجبها`, 
                         blockedCount === arabicTexts.length ? 'success' : 'error');
        }
        
        function clearResults() {
            document.getElementById('testLog').innerHTML = '';
            updateResults('تم مسح النتائج', 'info');
            log('🧹 تم مسح السجل', 'info');
        }
        
        function openDuties() {
            window.open('/duties/', '_blank');
            log('🔗 تم فتح صفحة كشف الواجبات', 'info');
        }
        
        function checkConsole() {
            log('🔍 افتح وحدة التحكم (F12) لمراقبة طلبات API', 'info');
            console.log('🔧 فحص إصلاح خطأ 404 - تم تحميل صفحة الاختبار');
        }
        
        function testAPI() {
            log('📡 محاكاة اختبار API...', 'info');
            
            // محاكاة طلبات API
            const testCases = [
                { id: '1', shouldWork: true },
                { id: 'البوابة الرئيسية', shouldWork: false },
                { id: '2', shouldWork: true },
                { id: 'موقع الحراسة', shouldWork: false }
            ];
            
            testCases.forEach((testCase, index) => {
                setTimeout(() => {
                    const result = validateLocationId(testCase.id);
                    if (result.valid === testCase.shouldWork) {
                        log(`✅ API Test ${index + 1}: "${testCase.id}" - النتيجة متوقعة`, 'success');
                    } else {
                        log(`❌ API Test ${index + 1}: "${testCase.id}" - النتيجة غير متوقعة`, 'error');
                    }
                }, index * 500);
            });
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 تم تحميل صفحة اختبار إصلاح 404', 'success');
            updateResults('النظام جاهز للاختبار', 'info');
            
            // اختبار تلقائي سريع
            setTimeout(() => {
                log('🔄 تشغيل اختبار تلقائي...', 'info');
                testValidIds();
                setTimeout(testInvalidIds, 1000);
                setTimeout(testArabicText, 2000);
            }, 1000);
        });
        
        // اختبار عند الضغط على Enter
        document.getElementById('locationIdTest').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                testLocationId();
            }
        });
    </script>
</body>
</html>
