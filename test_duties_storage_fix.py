#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import psycopg2
import json
from datetime import datetime

def test_duties_storage_system():
    """اختبار نظام حفظ البيانات المحسن لكشف الواجبات"""
    
    try:
        # الاتصال بقاعدة البيانات
        conn = psycopg2.connect('postgresql://postgres:postgres@localhost:5432/military_warehouse')
        cursor = conn.cursor()
        
        print("🔍 اختبار نظام حفظ البيانات المحسن لكشف الواجبات")
        print("=" * 60)
        
        # 1. فحص جدول DutyData
        print("\n📊 فحص جدول duty_data:")
        cursor.execute("SELECT COUNT(*) FROM duty_data")
        duty_count = cursor.fetchone()[0]
        print(f"   عدد السجلات: {duty_count}")
        
        if duty_count > 0:
            cursor.execute("""
                SELECT id, user_id, duty_date, duty_time, 
                       LENGTH(duty_data) as data_length, 
                       created_at, updated_at
                FROM duty_data 
                ORDER BY updated_at DESC 
                LIMIT 5
            """)
            
            recent_duties = cursor.fetchall()
            print(f"   آخر 5 سجلات:")
            for duty in recent_duties:
                duty_id, user_id, duty_date, duty_time, data_length, created_at, updated_at = duty
                print(f"     ID: {duty_id}, المستخدم: {user_id}, التاريخ: {duty_date}")
                print(f"     حجم البيانات: {data_length} حرف, آخر تحديث: {updated_at}")
                print()
        
        # 2. فحص جدول DutyTemplate (المسودات)
        print("\n📋 فحص جدول duty_templates (المسودات):")
        cursor.execute("SELECT COUNT(*) FROM duty_templates WHERE name LIKE 'مسودة_%'")
        draft_count = cursor.fetchone()[0]
        print(f"   عدد المسودات: {draft_count}")
        
        if draft_count > 0:
            cursor.execute("""
                SELECT id, name, created_by, 
                       LENGTH(template_data) as data_length,
                       created_at, updated_at
                FROM duty_templates 
                WHERE name LIKE 'مسودة_%'
                ORDER BY updated_at DESC
            """)
            
            drafts = cursor.fetchall()
            print(f"   المسودات الموجودة:")
            for draft in drafts:
                draft_id, name, created_by, data_length, created_at, updated_at = draft
                print(f"     ID: {draft_id}, الاسم: {name}, المستخدم: {created_by}")
                print(f"     حجم البيانات: {data_length} حرف, آخر تحديث: {updated_at}")
                print()
        
        # 3. فحص جدول ShiftsData
        print("\n👥 فحص جدول shifts_data:")
        cursor.execute("SELECT COUNT(*) FROM shifts_data")
        shifts_count = cursor.fetchone()[0]
        print(f"   عدد سجلات المناوبين: {shifts_count}")
        
        # 4. اختبار إنشاء بيانات تجريبية
        print("\n🧪 اختبار إنشاء بيانات تجريبية:")
        
        test_data = {
            "dutyData": {
                "headers": ["الرقم", "موقع الواجب", "من 6 مساءً إلى 10 ليلاً", "ملاحظات"],
                "rows": [
                    ["1", "البوابة الرئيسية", "أحمد محمد", "لا يوجد"],
                    ["2", "برج المراقبة", "سعد علي", "تم التسليم"]
                ]
            },
            "patrolData": {
                "headers": ["الرقم", "موقع الدورية", "الفترة الأولى", "ملاحظات"],
                "rows": [
                    ["1", "الدورية الخارجية", "محمد أحمد", "طبيعي"]
                ]
            },
            "shiftsData": {
                "headers": ["الرقم", "المنصب", "الاسم", "ملاحظات"],
                "rows": [
                    ["1", "مناوب السرية", "خالد سعد", "حاضر"]
                ]
            },
            "formData": {
                "dayName": "الأحد",
                "hijriDate": "15 محرم 1446",
                "gregorianDate": "2025-07-22",
                "receiptNumber": "TEST001"
            },
            "timestamp": datetime.now().isoformat()
        }
        
        # محاولة حفظ البيانات التجريبية
        try:
            # حفظ في duty_data
            cursor.execute("""
                INSERT INTO duty_data (location_id, user_id, duty_date, duty_time, duty_data, notes)
                VALUES (1, 1, %s, %s, %s, %s)
                RETURNING id
            """, (
                datetime.now().date(),
                datetime.now().time(),
                json.dumps(test_data, ensure_ascii=False),
                "اختبار النظام المحسن"
            ))
            
            test_duty_id = cursor.fetchone()[0]
            print(f"   ✅ تم إنشاء سجل تجريبي في duty_data: ID {test_duty_id}")
            
            # حفظ مسودة تجريبية
            cursor.execute("""
                INSERT INTO duty_templates (name, location_id, template_data, created_by)
                VALUES (%s, %s, %s, %s)
                ON CONFLICT (name) DO UPDATE SET
                template_data = EXCLUDED.template_data,
                updated_at = CURRENT_TIMESTAMP
                RETURNING id
            """, (
                "مسودة_اختبار",
                1,
                json.dumps(test_data, ensure_ascii=False),
                1
            ))
            
            test_template_id = cursor.fetchone()[0]
            print(f"   ✅ تم إنشاء مسودة تجريبية: ID {test_template_id}")
            
            conn.commit()
            
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء البيانات التجريبية: {e}")
            conn.rollback()
        
        # 5. اختبار استرجاع البيانات
        print("\n📥 اختبار استرجاع البيانات:")
        
        try:
            # استرجاع آخر سجل
            cursor.execute("""
                SELECT duty_data FROM duty_data 
                WHERE user_id = 1 
                ORDER BY updated_at DESC 
                LIMIT 1
            """)
            
            result = cursor.fetchone()
            if result:
                retrieved_data = json.loads(result[0])
                print(f"   ✅ تم استرجاع البيانات بنجاح")
                print(f"   📊 عدد صفوف الجدول الرئيسي: {len(retrieved_data.get('dutyData', {}).get('rows', []))}")
                print(f"   🚶 عدد صفوف جدول الدوريات: {len(retrieved_data.get('patrolData', {}).get('rows', []))}")
                print(f"   👥 عدد صفوف جدول المناوبين: {len(retrieved_data.get('shiftsData', {}).get('rows', []))}")
            else:
                print(f"   ⚠️ لا توجد بيانات للاسترجاع")
                
        except Exception as e:
            print(f"   ❌ خطأ في استرجاع البيانات: {e}")
        
        # 6. إحصائيات النظام
        print("\n📈 إحصائيات النظام:")
        
        # إحصائيات المستخدمين
        cursor.execute("""
            SELECT user_id, COUNT(*) as count, MAX(updated_at) as last_update
            FROM duty_data 
            GROUP BY user_id 
            ORDER BY count DESC
        """)
        
        user_stats = cursor.fetchall()
        print(f"   👤 المستخدمون النشطون:")
        for user_id, count, last_update in user_stats:
            print(f"     المستخدم {user_id}: {count} كشف, آخر نشاط: {last_update}")
        
        # إحصائيات التواريخ
        cursor.execute("""
            SELECT duty_date, COUNT(*) as count
            FROM duty_data 
            GROUP BY duty_date 
            ORDER BY duty_date DESC 
            LIMIT 5
        """)
        
        date_stats = cursor.fetchall()
        print(f"   📅 آخر 5 تواريخ:")
        for duty_date, count in date_stats:
            print(f"     {duty_date}: {count} كشف")
        
        conn.close()
        
        print("\n" + "=" * 60)
        print("✅ تم اختبار النظام بنجاح!")
        print("💡 التوصيات:")
        print("   1. النظام المحسن يحفظ البيانات في قاعدة البيانات أولاً")
        print("   2. localStorage يُستخدم كنسخة احتياطية فقط")
        print("   3. البيانات محمية من فقدان سجلات المتصفح")
        print("   4. الحفظ التلقائي يعمل كل 30 ثانية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")
        return False

if __name__ == "__main__":
    test_duties_storage_system()
