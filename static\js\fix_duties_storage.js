// إصلاح مشكلة حفظ البيانات في كشف الواجبات
// هذا الملف يحتوي على الحلول المطلوبة لضمان حفظ البيانات في قاعدة البيانات

console.log('🔧 تحميل إصلاحات نظام حفظ البيانات...');

// 1. تحسين دالة الحفظ التلقائي لتعطي الأولوية لقاعدة البيانات
function improvedAutoSave() {
    try {
        console.log('💾 بدء الحفظ التلقائي المحسن...');

        // جمع البيانات من الجداول (إذا كانت الدوال متاحة)
        if (typeof collectDataFromInputs === 'function') {
            collectDataFromInputs();
        }
        if (typeof collectPatrolDataFromInputs === 'function') {
            collectPatrolDataFromInputs();
        }
        if (typeof collectShiftsDataFromInputs === 'function') {
            collectShiftsDataFromInputs();
        }

        // جمع البيانات يدوياً إذا لم تكن الدوال متاحة
        collectDutyDataManually();
        
        const dataToSave = {
            dutyData: dutyData,
            patrolData: patrolData,
            shiftsData: shiftsData,
            formData: {
                dayName: document.getElementById('dayName').value,
                hijriDate: document.getElementById('hijriDate').value,
                gregorianDate: document.getElementById('gregorianDate').value,
                receiptNumber: document.getElementById('receiptNumber').value
            },
            timestamp: new Date().toISOString()
        };

        // 1. حفظ في قاعدة البيانات أولاً (الأولوية)
        saveToDatabaseFirst(dataToSave);
        
        // 2. حفظ في localStorage كنسخة احتياطية
        localStorage.setItem('dutyFormData', JSON.stringify(dataToSave));
        
        console.log('✅ تم الحفظ التلقائي المحسن');
        
    } catch (error) {
        console.error('❌ خطأ في الحفظ التلقائي المحسن:', error);
    }
}

// 2. دالة حفظ محسنة تعطي الأولوية لقاعدة البيانات
async function saveToDatabaseFirst(data) {
    try {
        console.log('🌐 حفظ في قاعدة البيانات (أولوية عالية)...');
        
        const response = await fetch('/duties/api/save-duty-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            },
            body: JSON.stringify(data)
        });

        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                console.log('✅ تم حفظ البيانات في قاعدة البيانات بنجاح');
                showSaveStatus('محفوظ في قاعدة البيانات', 'success');
                return true;
            } else {
                throw new Error(result.message || 'فشل في حفظ البيانات');
            }
        } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
    } catch (error) {
        console.error('❌ خطأ في حفظ البيانات في قاعدة البيانات:', error);
        showSaveStatus('خطأ في الحفظ - محفوظ محلياً فقط', 'warning');
        return false;
    }
}

// 3. دالة تحميل محسنة تعطي الأولوية لقاعدة البيانات
async function loadFromDatabaseFirst() {
    try {
        console.log('🌐 تحميل من قاعدة البيانات (أولوية عالية)...');
        
        const response = await fetch('/duties/api/load-duty-data');
        if (response.ok) {
            const result = await response.json();
            if (result.success && result.data) {
                console.log('✅ تم تحميل البيانات من قاعدة البيانات');
                applyLoadedData(result.data);
                showSaveStatus('تم تحميل البيانات من قاعدة البيانات', 'info');
                return true;
            }
        }
        
        // إذا فشل التحميل من قاعدة البيانات، جرب localStorage
        console.log('⚠️ لا توجد بيانات في قاعدة البيانات، محاولة التحميل من localStorage...');
        return loadFromLocalStorageBackup();
        
    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات من قاعدة البيانات:', error);
        return loadFromLocalStorageBackup();
    }
}

// 4. دالة تحميل احتياطية من localStorage
function loadFromLocalStorageBackup() {
    try {
        const savedData = localStorage.getItem('dutyFormData');
        if (savedData) {
            const data = JSON.parse(savedData);
            console.log('✅ تم تحميل البيانات من localStorage (نسخة احتياطية)');
            applyLoadedData(data);
            showSaveStatus('تم تحميل البيانات من النسخة الاحتياطية', 'warning');
            
            // محاولة حفظ البيانات في قاعدة البيانات
            saveToDatabaseFirst(data);
            return true;
        }
        return false;
    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات من localStorage:', error);
        return false;
    }
}

// 5. دالة عرض حالة الحفظ
function showSaveStatus(message, type = 'info') {
    // إنشاء عنصر الإشعار إذا لم يكن موجوداً
    let statusElement = document.getElementById('saveStatus');
    if (!statusElement) {
        statusElement = document.createElement('div');
        statusElement.id = 'saveStatus';
        statusElement.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            z-index: 9999;
            transition: all 0.3s ease;
        `;
        document.body.appendChild(statusElement);
    }
    
    // تحديد لون الخلفية حسب النوع
    const colors = {
        success: '#28a745',
        warning: '#ffc107',
        error: '#dc3545',
        info: '#17a2b8'
    };
    
    statusElement.style.backgroundColor = colors[type] || colors.info;
    statusElement.textContent = message;
    statusElement.style.display = 'block';
    
    // إخفاء الإشعار بعد 3 ثوان
    setTimeout(() => {
        statusElement.style.display = 'none';
    }, 3000);
}

// 6. دالة الحصول على CSRF Token
function getCSRFToken() {
    const metaTag = document.querySelector('meta[name="csrf-token"]');
    return metaTag ? metaTag.getAttribute('content') : '';
}

// 7. دالة تطبيق البيانات المحملة
function applyLoadedData(data) {
    try {
        console.log('📥 تطبيق البيانات المحملة...');

        // تطبيق بيانات النموذج
        if (data.formData) {
            const formData = data.formData;
            if (formData.dayName && document.getElementById('dayName')) {
                document.getElementById('dayName').value = formData.dayName;
            }
            if (formData.hijriDate && document.getElementById('hijriDate')) {
                document.getElementById('hijriDate').value = formData.hijriDate;
            }
            if (formData.gregorianDate && document.getElementById('gregorianDate')) {
                document.getElementById('gregorianDate').value = formData.gregorianDate;
            }
            if (formData.receiptNumber && document.getElementById('receiptNumber')) {
                document.getElementById('receiptNumber').value = formData.receiptNumber;
            }
        }

        // تطبيق بيانات الجداول (التحقق من وجود المتغيرات والدوال)
        if (data.dutyData && typeof dutyData !== 'undefined') {
            dutyData = data.dutyData;
            if (typeof generateTable === 'function') {
                generateTable();
            }
        }

        if (data.patrolData && typeof patrolData !== 'undefined') {
            patrolData = data.patrolData;
            if (typeof generatePatrolTable === 'function') {
                generatePatrolTable();
            }
        }

        if (data.shiftsData && typeof shiftsData !== 'undefined') {
            shiftsData = data.shiftsData;
            if (typeof generateShiftsTable === 'function') {
                generateShiftsTable();
            }
        }

        console.log('✅ تم تطبيق البيانات المحملة بنجاح');

    } catch (error) {
        console.error('❌ خطأ في تطبيق البيانات المحملة:', error);
    }
}

// 8. دالة الحفظ الفوري عند كل تغيير
function setupImprovedAutoSave() {
    console.log('🔧 إعداد نظام الحفظ التلقائي المحسن...');
    
    // حفظ عند تغيير أي حقل في النموذج
    const formFields = ['dayName', 'hijriDate', 'gregorianDate', 'receiptNumber'];
    formFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('input', () => {
                clearTimeout(window.autoSaveTimeout);
                window.autoSaveTimeout = setTimeout(improvedAutoSave, 1000);
            });
        }
    });
    
    // حفظ عند تغيير أي خلية في الجداول
    document.addEventListener('input', function(event) {
        if (event.target.classList.contains('editable-cell') ||
            event.target.classList.contains('patrol-editable-cell') ||
            event.target.classList.contains('shifts-editable-cell')) {
            
            clearTimeout(window.autoSaveTimeout);
            window.autoSaveTimeout = setTimeout(improvedAutoSave, 1000);
        }
    });
    
    // حفظ عند فقدان التركيز
    document.addEventListener('blur', function(event) {
        if (event.target.classList.contains('editable-cell') ||
            event.target.classList.contains('patrol-editable-cell') ||
            event.target.classList.contains('shifts-editable-cell')) {
            
            improvedAutoSave();
        }
    }, true);
    
    // حفظ دوري كل 30 ثانية
    setInterval(improvedAutoSave, 30000);
    
    console.log('✅ تم إعداد نظام الحفظ التلقائي المحسن');
}

// 9. دالة التهيئة الرئيسية
async function initializeImprovedDutySystem() {
    try {
        console.log('🚀 بدء تهيئة نظام كشف الواجبات المحسن...');
        
        // 1. تحميل البيانات من قاعدة البيانات أولاً
        const dataLoaded = await loadFromDatabaseFirst();
        
        // 2. إعداد نظام الحفظ التلقائي المحسن
        setupImprovedAutoSave();
        
        // 3. حفظ أولي للبيانات الحالية
        setTimeout(improvedAutoSave, 2000);
        
        console.log('✅ تم تهيئة نظام كشف الواجبات المحسن بنجاح');
        showSaveStatus('تم تهيئة النظام المحسن', 'success');
        
    } catch (error) {
        console.error('❌ خطأ في تهيئة النظام المحسن:', error);
        showSaveStatus('خطأ في تهيئة النظام', 'error');
    }
}

// 10. تصدير الدوال للاستخدام العام
window.improvedAutoSave = improvedAutoSave;
window.saveToDatabaseFirst = saveToDatabaseFirst;
window.loadFromDatabaseFirst = loadFromDatabaseFirst;
window.initializeImprovedDutySystem = initializeImprovedDutySystem;

// دالة لجمع البيانات يدوياً من الجداول
function collectDutyDataManually() {
    try {
        console.log('📊 جمع البيانات يدوياً من الجداول...');

        // جمع بيانات الجدول الرئيسي
        const mainTable = document.getElementById('dutyTable');
        if (mainTable && typeof dutyData !== 'undefined') {
            const rows = mainTable.querySelectorAll('tbody tr');
            rows.forEach((row, rowIndex) => {
                const cells = row.querySelectorAll('td input, td select, td');
                cells.forEach((cell, cellIndex) => {
                    if (cell.tagName === 'INPUT' || cell.tagName === 'SELECT') {
                        if (!dutyData.rows[rowIndex]) {
                            dutyData.rows[rowIndex] = [];
                        }
                        dutyData.rows[rowIndex][cellIndex] = cell.value || '';
                    }
                });
            });
        }

        // جمع بيانات جدول الدوريات
        const patrolTable = document.getElementById('patrolTable');
        if (patrolTable && typeof patrolData !== 'undefined') {
            const rows = patrolTable.querySelectorAll('tbody tr');
            rows.forEach((row, rowIndex) => {
                const cells = row.querySelectorAll('td input, td select, td');
                cells.forEach((cell, cellIndex) => {
                    if (cell.tagName === 'INPUT' || cell.tagName === 'SELECT') {
                        if (!patrolData.rows[rowIndex]) {
                            patrolData.rows[rowIndex] = [];
                        }
                        patrolData.rows[rowIndex][cellIndex] = cell.value || '';
                    }
                });
            });
        }

        // جمع بيانات جدول المناوبين
        const shiftsTable = document.getElementById('shiftsTable');
        if (shiftsTable && typeof shiftsData !== 'undefined') {
            const rows = shiftsTable.querySelectorAll('tbody tr');
            rows.forEach((row, rowIndex) => {
                const cells = row.querySelectorAll('td input, td select, td');
                cells.forEach((cell, cellIndex) => {
                    if (cell.tagName === 'INPUT' || cell.tagName === 'SELECT') {
                        if (!shiftsData.rows[rowIndex]) {
                            shiftsData.rows[rowIndex] = [];
                        }
                        shiftsData.rows[rowIndex][cellIndex] = cell.value || '';
                    }
                });
            });
        }

        console.log('✅ تم جمع البيانات يدوياً بنجاح');

    } catch (error) {
        console.error('❌ خطأ في جمع البيانات يدوياً:', error);
    }
}

console.log('✅ تم تحميل إصلاحات نظام حفظ البيانات بنجاح');
